export default {
  person: {
    name: '张三',
    hobby: '唱跳，rap，篮球',
  },
  header: {
    menu: {
      home: '首页',
      submit: '提交',
      browse: '浏览',
      statistic: '统计',
      download: '下载',
      help: '帮助',
    },
    auth: {
      login: '登录',
      register: '注册',
    },
    search: {
      placeholder: '搜索',
    },
    language: {
      english: 'English',
      chinese: '中文',
    },
    userMenu: {
      userCenter: '用户中心',
      mySubmission: '我的提交',
      setting: '设置',
      logout: '退出登录',
    },
  },
  help: {
    breadcrumb: '帮助',
    overview: {
      title: '概述',
      description:
        'NODE是一个生物大数据收集平台，包括实验样本信息收集、序列文件上传以及结果分析、共享和下载。NODE平台由六个主要模块组成：项目、样本、实验、批次、数据和分析。项目和样本彼此独立，但可以通过批次进行关联。通过这种方式，元数据和序列信息可以集成。',
      experimentType: {
        title: '1.1 支持的实验类型',
        description: 'NODE实验类型共有十四种，包括：',
        types: {
          genomic: '用于DNA测序。',
          transcriptomic: '用于RNA测序。',
          metagenomic: '用于从环境样本中所有微生物提取的DNA测序。',
          metatranscriptomic: '用于从环境样本中所有微生物提取的RNA测序。',
          genomicSingleCell: '用于单细胞DNA测序。',
          transcriptomicSingleCell: '用于单细胞RNA测序。',
          proteomic: '用于蛋白质测序。',
          metabolomic: '用于活体生物中的代谢物。',
          metabolomicNMR: '用于通过NMR检测活体生物中的代谢物数据。',
          electronMicroscopy: '用于生物样本的电子显微镜成像。',
          microarray: '用于微阵列数据。',
          synthetic: '用于合成生物学测序数据。',
          viralRNA: '用于病毒RNA测序。',
          flowCytometry: '用于确定细胞或细胞器的生物学特性数据。',
        },
      },
      sampleType: {
        title: '1.2 支持的样本类型',
        description: 'NODE样本类型共有八种，包括：',
        types: {
          human: '用于不涉及个人隐私问题的人类遗传资源相关的样本。',
          animalia:
            '用于来自常见实验动物的多细胞样本，例如小鼠、大鼠、斑马鱼等。',
          plant: '用于所有的植物样本。',
          cellLine: '用于来自常见实验室的细胞系。',
          microbe: '用于任何微生物样本或微生物菌株，例如酵母',
          environmentHost:
            '用于宏基因组和宿主相关样本，例如人类肠道宏基因组、人类口腔宏基因组、鱼类肠道宏基因组等。',
          environmentNonHost:
            '用于宏基因组和非宿主相关样本，例如海洋宏基因组、淡水宏基因组、土壤宏基因组等。',
          pathogen: '临床或宿主相关、环境、食品或其他病原体。',
        },
      },
    },
    registerLogin: {
      title: '注册和登录',
      signUp: {
        title: '2.1 注册NODE账户',
        step1: {
          title: '1.注册NODE',
          description:
            '输入您的邮箱地址、密码和确认密码，输入用户信息包括姓名、姓氏、组织和国家/地区来注册NODE，其他信息只需根据用户兴趣提供。选择"我已阅读并同意隐私政策"。点击"创建账户"。',
        },
        step2: {
          title: '2.注册完成',
          description: '您在NODE的注册已完成。',
        },
        step3: {
          title: '3.激活您的账户',
          description:
            '然后您将在注册邮箱中收到来自NODE的注册激活链接。请在48小时内点击链接激活您的账户。',
        },
        step4: {
          title: '4.激活完成',
          description:
            '激活后，您将收到提醒，表明您已完成注册的所有步骤。您可以开始登录NODE。',
        },
        tips: {
          title: '一些提示：',
          description:
            '请使用新的有效邮箱地址，并正确输入邮箱地址进行注册。否则您将收到以下提醒：',
          emailRegistered: '(1) 邮箱已注册',
          invalidEmail: '(2) 无效邮箱',
        },
      },
      login: {
        title: '2.2 登录NODE账户',
        description:
          '注册后，用户可以在登录页面输入正确的邮箱地址和密码来登录NODE。NODE提供"记住密码"功能，这样用户就不必每次使用NODE时都输入邮箱地址和密码。我们建议您只在个人电脑上激活此功能。',
      },
      resetPassword: {
        title: '2.3 忘记密码/重置密码',
        step1:
          '如果您忘记了密码，您可以简单地点击"忘记密码"，在下一页输入您的邮箱地址，然后点击发送，将密码重置链接发送到您的邮箱。',
        step2: '这是从NODE收到的密码重置邮件示例。',
        step3:
          '点击"密码重置"，用户可以进入NODE的密码重置页面。为了保护您的账户安全，我们强烈建议您不要分享您的密码给他人。',
      },
      modifyInfo: {
        title: '2.4 修改注册信息',
        description: '在用户中心，注册用户可以通过编辑修改注册信息。',
      },
    },
    userCenter: {
      title: '用户中心',
      overview: {
        title: '3.1 用户中心概览',
        description1:
          '用户中心是一个便捷的工具，用户可以概览自己的NODE数据以及管理数据申请。',
        description2:
          '在用户中心的以下部分，用户可以概览NODE数据、用户的详细信息以及与NODE中高访问率研究相关的高访问量文章信息，以及其他便于数据共享和使用的功能，如"我的共享"、"来自他人的共享"、"我的申请访问"、"来自他人的申请访问"和"我的审阅"。',
      },
      dataList: {
        title: '3.2 我的数据列表',
        description1:
          '"我的数据列表"显示用户的所有数据，包括项目、实验、样本、批次、分析、数据、发布和提交的列表。此外，还显示数据名称、状态、上传日期和操作。在实验、样本、批次和分析中，右侧图标"操作"按钮中有批量修改链接。',
        description2:
          '此外，您可以输入关键词和上传日期来搜索您的数据，然后过滤后的数据列表会显示在左侧的红色框中。',
      },
      dataStatistics: {
        title: '3.3 我的数据统计',
        description:
          '"我的数据统计"显示项目、样本、分析、实验、批次和数据的数据统计。用户可以看到数据的总量、可访问或不可访问数据的比例，以及项目、样本、分析、实验、批次和数据中文件的总量、可访问量或不可访问量。',
      },
      dataActivity: {
        title: '3.4 我的数据被访统计',
        description:
          '"我的数据被访统计"分别根据三种安全状态显示用户的项目、实验、样本、分析和总数据的数量。"数据查看"显示用户数据每月的图表数量。"数据下载"显示用户数据每月的下载次数图表。',
      },
    },
    detailPage: {
      title: '详情页面',
      projectDetail: {
        title: '4.1 项目详情',
        description:
          '在项目详情页面，项目信息包括项目ID、项目名称、项目描述、总数据量、总文件量、总样本量、实验ID、实验类型、实验名称、实验信息、样本类型、样本ID、样本名称、样本信息、文件类型、文件量、文件安全状态、数据列表、数据质量控制信息和下载链接。在数据质量控制信息中，用户可以在"操作"列中查看FASTQC报告和下载FASTQC报告。',
      },
      experimentDetail: {
        title: '4.2 实验详情',
        description:
          '在实验详情页面，实验信息包括实验ID、实验类型、实验名称、属性、样本类型、文件类型、文件量、文件安全状态、数据列表、数据质量控制信息和下载链接。在数据质量控制信息中，用户可以在"操作"列中查看FASTQC报告和下载FASTQC报告。',
      },
      sampleDetail: {
        title: '4.3 样本详情',
        description:
          '在样本详情页面，样本信息包括样本ID、样本类型、样本名称、物种名称、组织、属性、实验类型、文件类型、文件量、文件安全状态、数据列表、数据质量控制信息和下载链接。在数据质量控制信息中，用户可以在"操作"列中查看FASTQC报告和下载FASTQC报告。',
      },
      analysisDetail: {
        title: '4.4 分析详情',
        description:
          '在分析详情页面，分析信息包括分析ID、分析名称、分析类型、总数据量、总文件量、流水线信息、目标信息、数据列表和下载链接。',
      },
    },
    rawDataUpload: {
      title: '原始数据上传',
      description1:
        'NODE（多组学数据汇交平台）提供了一个集成的、兼容的、可比较的和可扩展的多组学资源平台，支持灵活的数据管理和有效的数据发布。NODE使用分层数据架构来支持多组学数据的存储，包括测序数据、基于MS的蛋白质组学数据、基于MS或NMR的代谢组学数据和荧光成像数据。',
      description2:
        'NODE中的数据提交过程包括三个步骤，包括"提交原始数据"、"提交元数据"和"归档"。下一页的箭头条通过突出显示特定步骤来指示提交过程的每个步骤。',
      description3:
        'NODE提供三种不同的数据上传方式，包括"Http上传"、"FTP上传"和"硬盘邮寄"。',
      httpUpload: {
        title: '5.1 HTTP上传',
        description:
          '通过网站上传小于200MB的文件。这种数据上传方法非常适合需要向NODE传输少量数据的用户。点击"选择您的文件"，然后选择您的文件并在未归档数据中查看文件。',
      },
      sftpUpload: {
        title: '5.2 SFTP上传',
        description:
          '如果数据太大无法使用http上传到NODE，用户可以申请Sftp上传。Sftp账户和密码是用户登录NODE的邮箱和密码。',
      },
      ftpTools: {
        title: '5.2.1 FTP工具',
        description: '我们建议使用FTP客户端软件上传文件，例如{fileZilla}',
        host: '主机：sftp://fms.biosino.org',
        username: '用户名：NODE用户名（邮箱）',
        password: '密码：NODE密码',
        port: '端口：44397',
      },
      commandLine: {
        title: '5.2.2 命令行',
        sftpTitle: 'SFTP',
        sftpCommand: 'sftp -oPort=44397',
        password: '密码：your-node-password',
        navigate: '导航到您需要的目标文件夹',
        putExample: 'put example.fastq.gz',
        lftpTitle: 'LFTP',
        lftpCommand1: 'lftp',
        lftpCommand2: 'lftp :~> connect',
        mputDescription: '您可以使用mput上传数据',
        mputCommand: 'mput *.gz',
        uncheckedDescription:
          '数据上传后，您可以点击"待校验"按钮在您的账户中找到数据。',
      },
      dataIntegrityCheck: {
        title: '5.2.3 数据完整性校验',
        description1:
          '通过FTP上传的数据需要进行MD5验证和完整性验证，以确保数据的完整性，然后才能正式进入Node。',
        description2: '点击主页"提交"进入提交系统。',
        description3:
          '数据上传后，您可以点击"待校验"按钮在您的账户中找到数据。选择需要完整性验证的文件或文件夹，然后点击"数据完整性校验"按钮。',
        description4:
          '数据提交完整性验证后，可以在"校验中"的"状态"列中查看完整性验证的进度。失败原因显示在"失败原因"列中。',
        description5:
          '已完成完整性验证的数据可以在"未归档数据"中查看。如果您想删除数据，可以在"操作"中逐个删除数据，或者可以选择所有要删除的数据，然后点击"删除"按钮。',
        tips: {
          title: '一些提示：',
          tip1: '1. 向FTP上传数据时，根据数据的实验类型和日期创建文件夹，将相同实验类型放在同一文件夹中，并上传整个文件夹。在完整性验证期间，可以一起检查整个文件夹中的数据。',
          tip2: '2. 每个文件夹中的文件不应超过4000个。',
        },
      },
      expressHardDrive: {
        title: '5.3 硬盘邮寄',
        description:
          '由于网络条件，大规模数据可能无法安全上传。用户可以使用硬盘将数据邮寄到NODE。邮寄前，请提前通知我们（+86 021-********）。邮寄后，请向我们提供您的邮寄详情。我们的联系信息如下：',
        email: '邮箱地址：',
        address:
          '地址：生物医学大数据中心，上海市岳阳路320号，200031 中华人民共和国',
      },
    },
    metadataArchiving: {
      title: '元数据和归档',
      description1:
        '成功上传后，数据将处于未归档状态。我们强烈建议用户在卸载数据后尽可能完整地填写元数据。如果您希望您的数据被搜索和访问，并与其他研究人员共享您的数据，这些信息至关重要。在NODE中，用户可以使用"元数据"功能来安排元数据信息，包括提交者、项目、实验、样本、批次。',
      description2:
        '有两种数据归档形式：原始数据和分析数据归档。原始数据归档主要用于归档元数据，分析数据归档用于归档由不同软件或工具分析的数据。',
      rawDataArchiving: {
        title: '6.1 原始数据归档',
        newSubmission: {
          title: '6.1.1 新提交',
          description1:
            '点击原始数据按钮和继续按钮后，用户将进入元数据归档界面。',
          description2: '以下是填写各部分详细元数据信息的指导。',
          submitter: {
            title: '提交者：',
            firstName: '名字：提交者的名字',
            lastName: '姓氏：提交者的姓氏',
            organization:
              '组织：提交者工作的组织名称，例如中科院上海营养与健康研究所',
            email: '邮箱：提交者的有效邮箱地址',
            countryRegion: '国家/地区：提交者来自的国家或地区。',
          },
          project: {
            title: '项目：',
            name: '项目名称：NODE允许用户将数据添加到现有项目或创建新的项目名称。',
            description: '描述：项目的描述',
            relatedLinks:
              '相关链接：与此项目相关的资源链接（文章信息、数据集、在线数据库）。用户可以使用加号按钮根据项目添加更多链接。',
          },
          experiment: {
            title: '实验：',
            name: '实验名称：NODE允许用户将数据添加到现有实验或创建新的实验名称。',
            description: '实验描述：实验的描述。',
            platform:
              '平台：用户用于进行实验的平台，NODE提供六种不同的选项，包括Illumina Hiseq 2000、Illumina Hiseq 2500、Illumina Hiseq 3000、ABI SOLiD 3、ABI SOLiD 4和Illumina Miseq。请根据您的实验设计选择正确的平台。',
            plannedReadLength: '计划读长：用户可以根据他/她的实验输入计划读长',
            matePair: '配对末端：用户可以根据他/她的数据选择Y/N。',
            libraryName: '文库名称：用户可以为他/她的文库定义一个名称',
            libraryStrategy:
              '文库策略：用于此文库的测序技术。NODE提供五个选项，包括WGS、EST、RNA-Seq、FINISHING、Chip-Seq。请根据您的实验设计选择正确的文库策略。',
            librarySource:
              '文库来源：指定正在测序的源材料类型。NODE提供三个选项供选择，包括基因组、转录组、宏基因组。请根据您的实验设计选择正确的文库来源。',
            librarySelection:
              '文库选择：用于在序列文库制备中富集目标的方法。NODE提供三个选项，包括PCR、RT-PCR和RANDOM。请根据您的实验设计选择正确的文库来源。',
            libraryLayout:
              '文库布局：序列扩增的方式。NODE提供三个选项，包括单端、配对、目标位点。请根据您的实验设计选择正确的文库来源。',
            libraryConstruction: '文库构建：简要描述用户构建文库的方式。',
            relatedLinks:
              '相关链接：您实验的相关链接，用户可以使用加号按钮根据您的实验添加更多链接。',
          },
          sample: {
            title: '样本：',
            name: '样本名称：NODE允许用户将数据添加到现有样本或创建新的样本名称。',
            organism:
              '样本的物种名称：用户用于制备样本的物种名称，例如：肝脏，意味着此样本来自肝脏。或一般来自人类。',
            description: '样本描述：样本的描述',
            attributes:
              '样本属性：如果有任何与样本相关的其他信息，用户无法在样本表的任何部分填写，请放在样本属性部分。用户可以使用加号或减号按钮分别添加或删除属性。',
            provider: '样本提供者：为实验分析提供样本的人员信息。',
            relatedLinks:
              '相关链接：与此样本或样本集相关的资源链接（文章信息、数据集、在线数据库）。用户可以使用加号按钮根据您的样本添加更多链接。',
          },
          run: {
            title: '批次：',
            name: '批次名称：NODE允许用户将数据添加到现有批次或创建新的批次名称。',
            description: '描述：批次的描述。',
          },
          archives: {
            title: '归档：',
            description:
              '新归档填写实验名称、样本名称、批次名称和数据ID。点击"检查并保存"按钮，然后点击"提交"按钮。',
            runDescription: '描述：批次的描述。',
          },
          submission: {
            title: '提交：',
            description:
              '归档后，在我的提交中，用户可以看到提交ID，用户可以撤销提交以重新编辑元数据。',
            runDescription: '描述：批次的描述。',
          },
        },
      },
      analysisDataArchiving: {
        title: '6.2 分析数据归档',
        newSubmission: {
          title: '6.2.1. 新提交',
          enterInterface: '点击分析数据按钮时，用户将进入分析数据归档界面。',
          guidance: '以下是填写分析数据信息的一些指导。',
          analysisName:
            '分析名称：NODE允许用户将数据添加到现有分析或创建新的分析名称。',
          analysisDescription: '分析描述：分析的描述。',
          program: '程序：用于分析数据的软件或工具名称。',
          link: '链接：与分析步骤相关的资源链接。',
          version: '版本：程序的版本。',
          output: '输出：选择分析数据对应的数据ID',
          target:
            '目标：通过点击每个ID号将数据与项目、实验、样本、批次、数据和分析关联。',
          archives: {
            title: '归档：',
            description:
              '新归档填写分析名称和数据ID。点击"检查并保存"按钮，然后点击"提交"按钮。',
          },
          submission: {
            title: '提交：',
            description:
              '归档后，在我的提交中，用户可以看到提交ID，用户可以撤销提交以重新编辑元数据。审阅通过后，用户可以收到提交的电子邮件。',
          },
        },
        reArchive: {
          title: '6.2.1 重新归档',
          description:
            '用户在用户中心的我的数据列表中逐一重新编辑分析；或者用户在元数据中批量修改分析。',
          batchModify:
            '如果用户想要批量修改分析，用户在分析多个中填写需要批量修改的分析ID及其完整信息。如果原始字段值更改为空值，系统默认删除并更改字段值。点击"检查并保存"按钮，然后在批量归档中提交。',
          addNewData:
            '如果用户想要在现有分析中添加新数据，用户在批量归档中填写分析ID和新数据ID。点击"检查并保存"按钮，然后在批量归档中提交。',
        },
      },
      reArchive: {
        title: '6.1.2 重新归档',
        description:
          '审阅通过后，用户可以重新编辑元数据，或批量修改元数据，或向现有项目添加新数据。如果用户想要重新编辑元数据，用户在用户中心的我的数据列表中逐一重新编辑项目、实验、样本和分析；或者用户在元数据中批量修改实验、样本和分析。',
        experimentBatch:
          '如果用户想要批量修改实验，用户在实验多个中填写需要批量修改的实验ID及其完整信息。如果原始字段值更改为空值，系统默认删除并更改字段值。点击"检查并保存"按钮，然后在批量归档中提交。',
        sampleBatch:
          '如果用户想要批量修改样本，用户在样本多个中填写需要批量修改的样本ID及其完整信息。如果原始字段值更改为空值，系统默认删除并更改字段值。点击"检查并保存"按钮，然后在批量归档中提交。',
        scenarios: {
          modifyRunName:
            '如果用户想要在现有项目中批量修改批次名称，用户在批量归档中填写需要批量修改的批次ID、新批次名称和数据ID。',
          addNewData:
            '如果用户想要在现有项目中添加新数据，用户在批量归档中填写实验ID、样本ID、新批次名称和新数据ID。',
          modifyDataNewRun:
            '如果用户想要在现有项目中为新批次名称批量修改数据，用户在批量归档中填写实验ID、样本ID、新批次名称和需要批量修改的数据ID。',
          addNewExperiment:
            '如果用户想要在现有项目中添加新实验、新样本和新数据，用户在实验多个中填写实验名称、项目ID和实验信息，在样本多个中填写样本名称和样本信息，并填写实验名称、样本名称、批次名称和数据ID。',
        },
        finalStep: '点击"检查并保存"按钮，然后在批量归档中提交。',
      },
    },
    shareReviewRequest: {
      title: '共享、审阅和申请访问',
      dataShare: {
        title: '10.1 数据共享',
        description1:
          '与其他研究人员共享您的数据对于让其他科学家了解您的研究并帮助其他科学家进行研究至关重要。在项目详情页面，当数据是私有或受限的且共享用户是NODE用户时，您可以使用共享按钮，如下图所示。类似的共享按钮也可以在样本、实验和分析详情页面找到。',
        description2:
          '点击共享按钮后，您可以通过点击项目、实验或样本前面的相应框来选择要共享的数据部分，并输入您希望与之共享此部分数据的人员的邮箱地址。用户可以在以下"我的共享"页面查看所有已共享的数据。被共享的人员需要是注册的NODE用户。',
        description3:
          '在我的共享页面，您可以看到所有共享的数据。红色的取消按钮可用于取消共享数据。',
        description4: '被共享的用户将收到来自NODE的邮件，通知数据共享成功。',
        description5:
          '然后他们可以在"来自他人的共享"下查看共享的数据，并通过点击蓝色的下载按钮下载他们感兴趣的数据。',
      },
      dataReview: {
        title: '10.2 数据审阅',
        description1:
          '如果数据所有者想要发布数据，此功能对于使数据对审阅者可见非常有用。在项目的详情页面，当数据受限时，用户可以使用被红色椭圆包围的审阅按钮，如下图所示。类似的审阅按钮也可以在样本、实验和分析详情页面找到。',
        description2:
          '用户可以使用此功能通过输入人员姓名、邮箱地址和他们希望查看数据的上传日期来选择特定人员查看数据。用户还可以通过点击项目、实验或样本前面的适当框来选择他/她希望审阅的数据部分。',
        description3:
          '一旦您确认了要审阅的数据，将显示"我的审阅"页面。您可以看到所有已检查的数据。',
        description4:
          '被审阅的用户将收到来自NODE的邮件，通过点击这里按钮下载成功审阅的数据。',
      },
      requestRestrictedData: {
        title: '10.3 受限数据申请访问',
        description1: '您必须向作者申请授权才能使用受限数据。',
        example: '例如：',
        userADescription:
          '用户A在NODE中有名为OEP00000775的受限数据，如下所示：',
        userBDescription1:
          '用户B可以搜索这个名为OEP000775的受限数据并访问它，但无法下载：',
        userBDescription2:
          '如果用户B想要下载数据，用户B必须通过发送申请向所有者申请授权。用户B可以选择需要的数据部分并等待用户A的许可。详细的申请理由可能有助于通过。',
        userBDescription3: '用户B可以在用户中心检查我的数据申请。',
        emailNotification: '然后用户A将收到来自NODE的邮件，如下所示：',
        checkRequests: '用户A可以在用户中心查看来自其他人的数据申请。',
        permitDecline:
          '用户A可以登录NODE并在用户中心找到数据申请以允许或拒绝此申请。',
        authorization:
          '如果用户A授权此申请，用户B将收到一个有效期为一个月的验证码。用户B只需点击ID跳转到下载页面，然后点击导出数据链接获取下载地址。',
      },
    },
    dataSecurity: {
      title: '数据安全',
      overview: {
        title: '8.1 概述',
        description1:
          'NODE提供三个级别的数据安全，包括公开数据、受限数据和私有数据。公开数据可以被任何使用NODE的人搜索、查看和下载。受限数据只能使用NODE中的搜索功能进行搜索。如果您想访问或下载这部分数据，您需要获得数据所有者的许可。私有数据只能由数据所有者查看。',
        description2:
          '一旦您上传了数据，除非您更改其安全级别，否则它会自动被视为私有。请注意，NODE只允许数据从高安全级别转换为低安全级别。例如，您只能将私有数据更改为受限数据或公开数据，否则不被允许。您也可以将受限数据转换为公开数据，但不能将公开数据转换为受限数据。',
        imageCaption: 'NODE中数据安全的变更',
        description3:
          'NODE鼓励用户共享您的数据，以便其他科学家了解您的研究并帮助其他科学家进行研究。',
        note: '注意：只有数据所有者可以进行以下更改',
      },
      privateToRestricted: {
        title: '8.2 私有->受限',
        description1:
          '作为数据的所有者，您可以转到项目、实验、样本或分析详情页面，使用安全功能更改数据的安全级别。',
        description2:
          '一旦您将数据的安全级别从私有更改为受限，您就迈出了共享数据的步骤。数据将可在NODE上浏览。但是，其他用户仍然无法查看或下载数据。另一方面，您可以使用"直到"功能选择您希望保持数据受限的时间长度，一年或两年。在此期间结束时，数据将自动发布。另一方面，如果您激活"需要申请"功能，其他用户将必须申请授权以申请访问数据。',
        description3:
          '一旦您决定了如何保持您的数据，您可以点击"确认"，NODE将显示您的数据状态更改的另一个确认，如下所示：',
      },
      privateToPublic: {
        title: '8.3 私有->公开',
        description:
          '一旦您将数据的安全级别从受限更改为公开，这意味着任何登录NODE的人都可以搜索、访问和下载您的数据，数据的质量控制信息也将同步公开。此外，NODE将显示一个确认，让您重新确认数据的安全状态。',
      },
      restrictedToPublic: {
        title: '8.4 受限->公开',
        description1:
          '将安全状态从受限更改为公开与将安全状态从私有更改为公开相同。',
        description2:
          '如果数据的安全级别从私有更改为受限或公开，项目、实验、样本和批次的可见性将从不可访问更改为可访问，即其他NODE用户将能够查看相关信息。',
      },
      tips: {
        title: '一些提示：',
        description:
          '以下三种更改数据安全状态的方式不被NODE允许。如果您必须进行以下更改，请联系',
        restriction1: '1. 公开-------->私有',
        restriction2: '2. 公开-------->受限',
        restriction3: '3. 受限-------->私有',
      },
    },
    nodeSearch: {
      title: 'NODE搜索',
      fullTextSearch: {
        title: '9.1 全文搜索',
        description:
          'NODE提供全文搜索。虽然NODE搜索的结果不会很精确，但用户可以获得与搜索输入关键词相关的所有信息。用户可以使用左侧面板中的信息进一步获取他/她感兴趣的详细信息。例如，如果用户在NODE中搜索"DNA"，他/她可以获得总共51576个结果。在结果页面的右侧面板上，用户可以找到根据数据访问、NODE级别和多组学等计算的结果数量，以进一步过滤搜索结果。',
      },
      advancedSearch: {
        title: '9.2 高级搜索',
        description:
          '在高级搜索中，用户可以选择ID、名称、描述等，并通过"AND"、"OR"、"NOT"组合ID、名称、描述等来搜索您想要搜索的数据。',
      },
    },
    dataManagement: {
      title: '数据管理',
      rawDataManagement: {
        title: '7.1 原始数据管理',
        description: '待校验的数据文件可以在提交页面的"待校验"表中删除。',
      },
      metaDataManagement: {
        title: '7.2 元数据管理',
        description:
          '在用户中心，我的数据列表的项目部分显示用户上传项目的基本信息。在项目页面，用户可以查看所有上传的项目列表页面，用户可以点击每个项目ID并查看不同项目的元数据信息详情。如果项目不可访问，用户可以删除项目。',
      },
    },
    humanGeneticResources: {
      title: '人类遗传资源数据提交',
      description:
        '如果您计划向我们提交包含人类遗传信息的数据，请遵守适用的法律或法规。同时，请确保您提交的数据在我们的数据提交工具中被设置为“限制”状态。此外，您的提交不得侵犯参与者的权益，并应包含其原始签署的知情同意书。有关具体管理规定和规则，请参阅',
      and: '和',
    },
    dataDownload: {
      title: '数据下载',
      httpDownload: {
        title: '11.1 HTTP下载',
        bulkDownload: {
          title: '1.批量下载',
          description:
            '使用NODE中的"下载"功能下载与一个项目相关的所有链接。强烈推荐使用此功能下载大量数据。',
          example: '以下是与项目"OEP000035"相关的所有链接的示例。',
        },
        specificDownload: {
          title: '2.下载特定数据',
          description:
            '通过点击下载按钮逐个下载文件。此功能更适合用于下载研究人员感兴趣的特定数据。',
        },
      },
      ftpDownload: {
        title: '11.2 FTP下载',
        tips: '提示：',
        directoryStructure:
          '数据下载目录按Run id排列，格式为/Public/byRun/OER/OER/OER。',
        byRun: '"byRun"用于原始数据下载',
        byAnalysis: '"byAnalysis"用于分析数据下载',
        runListQuestion: '在哪里可以获得批次列表？',
        runListAnswer: '在项目详情页面使用"导出数据链接"。',
        windows: {
          title: '(1)Windows',
          description: '我们建议使用FTP客户端软件上传文件，例如FileZilla',
          host: '主机：sftp://fms.biosino.org',
          username: '用户名：NODE用户名（邮箱）',
          password: '密码：NODE密码',
          port: '端口：44398',
          quickConnect: '点击"快速连接"进行数据下载。',
        },
        linux: {
          title: '(2)Linux',
          sftp: {
            title: 'SFTP',
            password: '密码：your-node-password',
            navigate: '导航到您需要的目标文件夹',
            command:
              'cd /Public/byRun/OER**/OER****/OER****** get example.fastq.gz',
          },
          lftp: {
            title: 'LFTP',
            password: '密码：your-node-password',
            description: '您可以使用glob pget或mget下载数据',
            command1: 'glob pget /Public/byRun/OER**/OER****/OER******/*.gz',
            command2: 'mget /Public/byRun/OER**/OER****/OER******/*.gz',
          },
        },
      },
    },
    faq: {
      title: '常见问题',
      activationFailed: {
        title: '13.1 登录时"激活失败"',
        description:
          '登录过程中出现"激活失败"错误消息可能是由链接超时或用户重复点击链接引起的。用户可以尝试登录，如果无法登录，用户可以联系',
        additionalInfo: '。或者用户复制并粘贴不完整的链接。',
      },
      usernamePasswordError: {
        title: '13.2 登录时"用户名或密码错误"',
        description:
          '如果在登录过程中显示"用户名或密码错误"错误消息，用户可以确认"用户名"（邮箱）或"密码"是否正确。如果用户忘记密码，用户可以点击"忘记密码"按钮找回密码。',
      },
      setDataSecurity: {
        title: '13.3 在审稿的过程中设置数据安全级别',
        description:
          '在审稿的过程中，数据状态可能受到限制。如何设置安全级别请参见',
        linkText: 'NODE中数据安全的变更。',
      },
      citeData: {
        title: '13.4 在稿件中引用数据',
        description:
          '成功将数据提交到NODE后，我们建议使用以下措辞在您的手稿中描述数据存储：',
        accessText: '所有数据都可以在NODE（',
        accessionText: '）中通过登录号XXX（例如，OEP00000073）或通过URL访问：',
        citationPrompt: '请引用以下文章信息：',
        publicationText:
          'Advances in multi-omics big data sharing platform research. Chinese Bulletin of Life Sciences. 2023, 35(12): 1553-1560. DOI:',
      },
      useTemplate: {
        title: '13.5 使用模板示例文件',
        description1: '在元数据提交过程中，用户对如何填写字段内容感到困惑。',
        description2: '用户可以下载excel示例查看字段内容的示例。',
      },
      integrityVerification: {
        title: '13.6 完整性验证未完成',
        description:
          '提交原始数据后，完整性验证长时间未完成。用户可以查看失败原因，并联系邮箱地址：',
      },
      submissionReview: {
        title: '13.7 提交数据后长时间未审核',
        description: '提交数据后，长时间未收到审核结果。用户可以联系 ',
      },
      splitMd5: {
        title: '13.8 如何拆分md5文件',
        description1:
          '用户经常从测序公司获得以下md5文档，例如"md5_result-1.txt"：',
        description2: '准备以下拆分脚本文件',
        description3: '使用以下命令批次脚本文件：',
      },
      dataIntegrityCheck: {
        title: '13.9 如何进行数据完整性校验',
        description: '如何进行数据完整性校验请参见',
        linkText: '5.2.3 数据完整性校验。',
      },
      transferData: {
        title: '13.10 如何将我的数据转移给另一个用户',
        description:
          '在NODE系统中，您可以灵活地将已归档和审核的元数据和原始数据的所有权（包括项目、实验、样本和分析）转移给NODE生态系统内的其他注册用户。要开始此过程，请按照以下简单步骤操作：',
        step1: '(1) 使用与您的NODE账户关联的邮箱地址撰写邮件。',
        step2: '(2) 将此邮件发送至',
        step2Additional: '，确保您还在抄送字段中包含收件人的邮箱地址。',
        step3:
          '(3) 在邮件正文中，清楚地概述您的需求，将当前在您的NODE账户（指定为XXX）下的特定数据（称为XXXX）转移到另一个指定的NODE账户（也称为XXX）。请确保发送方和接收方账户都在NODE系统中注册，并且您是数据的合法所有者。',
        step4:
          '(4) 收到您的邮件后，NODE支持团队将接管，指导您完成数据转移程序的其余部分。',
        conclusion:
          '我们在这里确保顺利过渡，所以如果您有任何问题或需要进一步帮助，请不要犹豫联系我们的支持团队。',
      },
      groupSubmissions: {
        title: '13.11 同一研究组多人数据提交',
        description:
          '用户经常询问如何促进同一研究组多人的数据提交。我们建议以下策略：',
        recommendation:
          '首先，每个研究组都应该有一个专用账户，通常是主要研究者（PI）常用的邮箱地址，作为该组数据的所有者。',
        strategy1:
          '策略1：组内不同成员，如学生和工作人员，使用各自的账户（邮箱地址）注册并提交数据。数据提交和归档完成后，他们可以主动从提交邮箱发送邮件至{email}，申请将数据转移到组的固定账户（详情请参考第',
        strategy1Additional: '节）。',
        strategy2:
          '策略2：组内不同成员，如学生和工作人员，使用各自的账户（邮箱地址）注册并提交数据。在数据提交过程中，他们应该完整提供PI的邮箱信息（详情请参考第',
        strategy2Additional:
          '节提交者）。随后，PI可以通过此邮箱申请通过node-support将数据转移到自己的邮箱账户。',
        strategy3:
          '策略3：研究组内的账户信息由内部维护。当需要数据提交时，账户信息与负责数据提交的人员共享。',
        conclusion:
          '这些策略确保了结构化和高效的数据提交方法，在研究组内保持清晰度和问责制。',
      },
    },
    toc: {
      updated: '已更新',
      overview: {
        title: '概述',
        experimentType: '支持的实验类型',
        sampleType: '支持的样本类型',
      },
      registerLogin: {
        title: '注册和登录',
        signUp: '注册NODE账户',
        login: '登录NODE账户',
        resetPassword: '忘记密码/重置密码',
        modifyInfo: '修改注册信息',
      },
      userCenter: {
        title: '用户中心',
        overview: '用户中心概览',
        dataList: '我的数据列表',
        dataStatistics: '我的数据统计',
        dataActivity: '我的数据被访统计',
      },
      detailPage: {
        title: '详情页面',
        project: '项目详情',
        experiment: '实验详情',
        sample: '样本详情',
        analysis: '分析详情',
      },
      rawDataUpload: {
        title: '原始数据上传',
        httpUpload: 'HTTP上传',
        sftpUpload: 'SFTP上传',
        ftpTools: 'FTP工具',
        commandLine: '命令行',
        dataIntegrityCheck: '数据完整性校验',
        expressHardDrive: '硬盘邮寄',
      },
      metadataArchiving: {
        title: '元数据和归档',
        rawDataArchiving: '原始数据归档',
        analysisDataArchiving: '分析数据归档',
        newSubmission: '新提交',
        reArchive: '重新归档',
      },
      dataManagement: {
        title: '数据管理',
        rawData: '原始数据管理',
        metaData: '元数据管理',
      },
      dataSecurity: {
        title: '数据安全',
        overview: '概述',
        privateToRestricted: '私有->受限',
        privateToPublic: '私有->公开',
        restrictedToPublic: '受限->公开',
      },
      nodeSearch: {
        title: 'NODE搜索',
        fullTextSearch: '全文搜索',
        advancedSearch: '高级搜索',
      },
      shareReviewRequest: {
        title: '共享、审阅和数据申请',
        dataShare: '数据共享',
        dataReview: '数据审阅',
        requestRestrictedData: '受限数据申请',
      },
      dataDownload: {
        title: '数据下载',
        httpDownload: 'HTTP下载',
        ftpDownload: 'FTP下载',
      },
      humanGeneticResources: '人类遗传资源数据提交',
      faq: {
        title: '常见问题',
        activationFailed: '登录时"激活失败"',
        usernamePasswordError: '登录时"用户名或密码错误"',
        setDataSecurity: '在审稿的过程中设置数据安全级别',
        citeData: '在稿件中引用数据',
        useTemplate: '使用模板示例文件',
        integrityVerification: '完整性验证未完成',
        submissionReview: '提交数据后长时间未审核',
        splitMd5: '如何拆分md5文件',
        dataIntegrityCheck: '如何进行数据完整性校验',
        transferData: '如何将我的数据转移给另一个用户',
        groupSubmissions: '同一研究组多人数据提交',
      },
    },
  },
  submit: {
    submission: {
      list: {
        breadcrumb: '我的提交',
        startNewSubmission: '开始新提交',
        submissionList: '提交列表',
        steps: {
          uploadData: '上传数据',
          metadata: '元数据',
          archivingData: '归档数据',
        },
        descriptions: {
          uploadData:
            'NODE接受各种组学数据和相应的分析数据，如基因组学、转录组学、宏基因组学、蛋白质组学数据等。在上传原始数据之前需要个人账户。有三种方式可以完成原始数据上传：HTTP、FTP、快递。还需要为所有上传的数据提供MD5校验文件。',
          metadata:
            'NODE元数据指定了关于原始数据的相关信息，有助于识别数据的特征。例如，原始数据集的文章信息通常在项目中描述，测序方法详细信息在实验中显示，源材料在样本中说明。',
          archiving:
            '在这一部分，原始数据已经上传到存储位置，元数据已经创建。原始数据在数据库中表示为批次对象，可以将原始数据与元数据关联。因此，归档的功能是通过构建批次对象将元数据与原始数据链接。归档后，整个数据发布过程就完成了。',
        },
      },
      detail: {
        common: {
          publication: {
            journal: '期刊',
            doi: 'DOI',
            pmid: 'PMID',
            title: '标题',
            reference: '引用',
          },
        },
      },
    },
    data: {
      rawdata: '原始数据',
      metadata: '元数据',
      archiving: '归档',
    },
    common: {
      dataUpload: '数据上传',
      route: '方式',
      upload: '上传',
      expressHardDrive: '硬盘邮寄',
      continueSubmitMetadata: '继续提交元数据',
    },
    messages: {
      uploadSuccessful: '上传成功',
      fileSizeExceeded: '上传文件大小不能超过200MB！',
      expressNameRequired: '快递名称为必填项',
      trackingNumberRequired: '快递单号为必填项',
      filePathRequired: '文件路径为必填项',
      submittedSuccessfully: '提交成功',
      tooMuchUncheckedData:
        '您有太多未进行完整性检查的数据。目前只显示最近的10,000条记录。如果您需要查看其他历史数据，请缩小时间范围并过滤',
      onlyComputerSupported: '数据提交仅支持在电脑上进行。',
    },
    downloadTemplate: {
      title: '使用内置表格编辑器还是上传Excel文件来提供您的{title}信息？',
      templateDescription: '{title}包{dataType}的模板；版本2.0',
      downloadExcel: '下载Excel',
      downloadExample: '下载示例',
      attributesPagePrefix: '有关列说明和示例，请参见',
      attributesPageSuffix: '属性页面。',
      uploadDescription: '上传包含每个{titles}属性的Excel文件',
      dragUploadText: '将文件拖拽到此处或',
      clickUploadText: '点击上传',
    },
    previewPublish: {
      publications: '文章信息',
      pmidPrefix: '( PMID: ',
      doiPrefix: '( DOI: ',
    },
    recommendIcon: {
      message: '建议填写此字段',
    },
    recommendTip: {
      prefix: '推荐信息：至少',
      one: '一个',
      suffix: '字段是必填的。',
    },
    resultLog: {
      title: '错误信息',
      export: '导出',
      columns: {
        row: '行',
        column: '列',
        value: '值',
        errorMessage: '错误信息',
      },
    },
    rawdata: {
      title: '待归档数据是否在数据列表中？',
      rule1:
        '文件可使用 {compress1} 或 {compress2} 压缩，也可打包成 tar 归档文件（非强制要求）。',
      rule2:
        '所有文件名必须唯一，且不得包含任何敏感信息。- 所有待归档的数据都需要在归档表中列出。一个批次（run）包含的数据文件数量取决于测序类型，双端测序包含正向和反向两个文件，单端测序包含一个文件。',
      rule3: '- 建议将所有新提交的文件置于单一目录下。',
      rule4:
        '必须为每个文件提供对应的 {md5} 文件，MD5文件中仅含对应文件的 32 位 MD5 值，并且文件名与对应文件完全一致。',
      rule5:
        '使用FTP方式上传的数据可以在“待校验”中查看，使用HTTP方式上传的数据可以在“我的数据->未归档数据”中查看。',
      tabs: {
        unchecked: '待校验 ({count})',
        checking: '校验中 ({count})',
        unarchivedData: '未归档数据 ({count})',
      },
      unarchivedTable: {
        search: {
          placeholder: '搜索 [数据ID、文件名、MD5]',
          period: '上传日期',
          rangeSeparator: '至',
          startDate: '开始日期',
          endDate: '结束日期',
          searchBtn: '搜索',
        },
        table: {
          dataId: '数据ID',
          fileName: '文件名',
          editFileName: '编辑文件名',
          sourcePath: '源路径',
          dataType: '数据类型',
          uploadType: '上传类型',
          fileSize: '文件大小',
          uploadDate: '上传日期',
          operate: '操作',
          delete: '删除',
        },
        actions: {
          export: '导出',
          delete: '删除',
        },
        dialog: {
          title: '编辑文件名',
          notice: '注意：只能编辑文件名，但不能编辑文件后缀。',
          cancel: '取消',
          confirm: '确认',
        },
        messages: {
          selectFilesToDelete: '请选择需要删除的文件',
          confirmDelete: '以下未归档数据将被删除。您要继续吗？<br>{dataIds}',
          exportAll: '将导出所有未归档表格数据',
          exportSelected: '将导出选中的未归档表格数据',
          saving: '保存中...',
        },
      },
      uncheckedTable: {
        search: {
          placeholder: '搜索 [文件名]',
          period: '上传日期',
          rangeSeparator: '至',
          startDate: '开始日期',
          endDate: '结束日期',
          searchBtn: '搜索',
        },
        table: {
          fileName: '文件名',
          uploadDate: '上传日期',
          uploadType: '上传类型',
          size: '大小',
          md5File: 'MD5文件',
        },
        md5Status: {
          provided: '已提供',
          notprovided: '未提供',
          invalidFormat: 'MD5格式无效',
        },
        md5Tooltip: {
          content:
            '您需要为文件提供MD5文件。<br>1) 确保MD5文件名与该文件的名称匹配。例如 sample1.fastq.gz 和 sample1.fastq.gz.md5。<br>2) 确保MD5文件只包含单个文件的32位MD5值。例如 9ef6aeca276489b64bd777d0ca2b1e9a sample1.fastq.gz<br>3) md5文件及其对应的原始数据文件应放置在同一文件夹中。',
        },
        actions: {
          dataIntegrityCheck: '数据完整性校验',
          export: '导出',
          delete: '删除',
        },
        dialog: {
          title: '数据完整性校验',
          note: '注意：',
          rule1:
            '1：您需要为文件提供MD5文件。确保其中只包含单个文件的32位MD5值，并且文件名与该文件的名称一致。例如',
          and: '和',
          rule2:
            '2. 在Linux系统中，可以使用{md5sum}命令计算MD5。如需更多信息，请联系{email}',
          rule3:
            '3：完整性检查完成后，数据会自动移动到{myData}的{unarchivedData}中。您可以在"未归档数据"列表中通过数据完整性校验查看数据。',
          rule4:
            '4：请确保文件路径和名称不包含空格、&符号、百分号(%)、星号(*)或希腊字母等特殊字符。建议使用大写字母(A-Z)、小写字母(a-z)、数字(0-9)、下划线(_)和连字符(-)的组合来构建文件名。',
          rule5: '5：完整性检查需要一段时间，请耐心等待。',
          unarchivedData: '未归档数据',
          myData: '我的数据',
          confirmMessage: '以下文件将进行数据完整性验证。您要继续吗？',
          filterWarning: '未提供MD5文件的文件将被过滤掉',
          confirm: '确认',
          cancel: '取消',
        },
        messages: {
          noValidFileSelected:
            '未选择有效文件。只有提供了MD5文件的文件才能被检查。',
          requestProcessing: '请求处理中！',
          selectFilesToCheck: '请选择需要检查的文件',
          confirmDeleteFiles: '以下文件将被删除。您要继续吗？<br>{files}',
          exportAll: '将导出所有待校验的表格数据',
          exportSelected: '将导出选中的待校验表格数据',
        },
      },
      preArchivedTable: {
        search: {
          placeholder: '搜索 [文件名]',
          period: '上传日期',
          rangeSeparator: '至',
          startDate: '开始日期',
          endDate: '结束日期',
          searchBtn: '搜索',
          exportTooltip: '如果您不选择行，默认将导出您搜索的所有数据。',
        },
        table: {
          dataId: '数据ID',
          fileName: '文件名',
          analysis: '分析',
          project: '项目',
          experiment: '实验',
          sample: '样本',
          run: '批次',
          uploadType: '上传类型',
          fileSize: '文件大小',
          uploadDate: '上传日期',
        },
        actions: {
          export: '导出',
          cancelArchive: '取消归档',
        },
        messages: {
          exportAll: '将导出所有表格数据',
          exportSelected: '将导出选中的表格数据',
        },
        fileNames: {
          archiving: '归档中',
          unarchived: '未归档',
        },
      },
      checkTable: {
        search: {
          placeholder: '搜索 [文件名]',
          status: '状态',
          period: '上传日期',
          rangeSeparator: '至',
          startDate: '开始日期',
          endDate: '结束日期',
          searchBtn: '搜索',
        },
        filter: {
          status: '状态',
          all: '全部',
          queuing: '排队中',
          checking: '校验中',
          failed: '校验失败',
        },
        table: {
          fileName: '文件名',
          uploadDate: '上传日期',
          status: '状态',
          failedCause: '失败原因',
        },
        status: {
          queuing: '排队中',
          checking: '校验中',
          failed: '校验失败',
          checkSuccess: '校验成功',
        },
        actions: {
          dataIntegrityCheck: '数据完整性校验',
          export: '导出',
          delete: '删除',
        },
        messages: {
          selectFailedFiles: '请选择校验失败的文件并重新校验',
          confirmReVerify: '这些文件将被重新校验。您要继续吗？<br>{files}',
          selectFilesToDelete: '请选择需要删除的文件',
          confirmDelete: '以下文件将被删除。您要继续吗？<br>{files}',
          exportAll: '将导出所有检查表格数据',
          exportSelected: '将导出选中的检查表格数据',
        },
      },
      archivedRawDataTable: {
        search: {
          placeholder: '搜索 [文件名]',
          period: '上传日期',
          rangeSeparator: '至',
          startDate: '开始日期',
          endDate: '结束日期',
          searchBtn: '搜索',
          exportTooltip: '如果您不选择行，默认将导出您搜索的所有数据。',
        },
        table: {
          dataId: '数据ID',
          fileName: '文件名',
          project: '项目',
          experiment: '实验',
          sample: '样本',
          run: '批次',
          fileSize: '文件大小',
          uploadDate: '上传日期',
        },
        actions: {
          export: '导出',
        },
        messages: {
          exportAll: '将导出所有表格数据',
          exportSelected: '将导出选中的表格数据',
        },
        fileNames: {
          archivedRawData: '已归档原始数据',
        },
      },
      archivedAnalysisDataTable: {
        search: {
          placeholder: '搜索 [文件名或文件路径]',
          period: '上传日期',
          rangeSeparator: '至',
          startDate: '开始日期',
          endDate: '结束日期',
          searchBtn: '搜索',
          exportTooltip: '如果您不选择行，默认将导出您搜索的所有数据。',
        },
        table: {
          dataId: '数据ID',
          fileName: '文件名',
          analysis: '分析',
          fileSize: '文件大小',
          uploadDate: '上传日期',
        },
        actions: {
          export: '导出',
        },
        messages: {
          exportAll: '将导出所有表格数据',
          exportSelected: '将导出选中的表格数据',
        },
        fileNames: {
          archivedAnalysisData: '已归档分析数据',
        },
      },
    },
    metadata: {
      selectMetaData: {
        breadcrumb: '元数据',
        title: '选择最能描述您提交内容的选项',
        rawData: {
          title: '原始数据',
          description:
            '选择此步骤来归档各种组学数据，如基因组学、转录组学、宏基因组学fastq数据、蛋白质组学原始数据等。',
        },
        analysisData: {
          title: '分析数据',
          description:
            '选择此步骤来归档与组学数据相关的分析数据，如vcf、bam、tsy、txt文件等。',
        },
        continueBtn: '继续',
      },
      rawData: {
        breadcrumb: '元数据',
        navigation: {
          submitter: '提交者',
          project: '项目',
          experiment: '实验',
          sample: '样本',
          archiving: '归档',
          multiple: '批量',
          single: '单个',
        },
        submitter: {
          title: '提交者',
          form: {
            firstName: '名',
            middleName: '中间名',
            lastName: '姓',
            organization: '组织机构',
            department: '部门',
            piName: 'PI姓名',
            email: '邮箱',
            phone: '电话',
            fax: '传真',
            street: '街道',
            city: '城市',
            stateProvince: '州/省',
            postalCode: '邮政编码',
            countryRegion: '国家/地区',
            countryPlaceholder: '请选择国家',
          },
          buttons: {
            continue: '继续',
            previewSave: '预览并保存',
            reset: '重置',
          },
          preview: {
            title: '预览',
            submitterTitle: '提交者',
            firstName: '名',
            middleName: '中间名',
            lastName: '姓',
            organization: '组织机构',
            department: '部门',
            piName: 'PI姓名',
            email: '邮箱',
            phone: '电话',
            fax: '传真',
            street: '街道',
            city: '城市',
            stateProvince: '州/省',
            postalCode: '邮政编码',
            countryRegion: '国家/地区',
            save: '保存',
            backEdit: '返回编辑',
          },
          validation: {
            firstNameRequired: '请输入名',
            lastNameRequired: '请输入姓',
            nameLength: '长度应为1到20个字符',
            organizationRequired: '请输入组织机构',
            emailRequired: '请输入邮箱地址',
            emailFormat: '请输入正确的邮箱地址',
            countryRequired: '请选择国家/地区',
          },
          messages: {
            loading: '加载中，请稍候...',
            saveSuccess: '提交者信息已成功保存，您现在可以继续下一步！',
            saveError: '保存"提交者"信息失败。请检查信息是否填写正确',
            unsavedConfirm: '您填写的数据尚未保存。您确定要跳过当前步骤吗？',
            mobileWarning: '数据提交仅支持在电脑上进行。',
          },
        },
        project: {
          title: '基本信息',
          form: {
            projectId: '项目ID',
            projectIdPlaceholder: '检查后将自动分配。',
            projectName: '项目名称',
            projectDescription: '项目描述',
          },
          buttons: {
            continue: '继续',
            previewSave: '预览并保存',
            reset: '重置',
            delete: '删除',
          },
          preview: {
            title: '预览',
            projectTitle: '项目',
            projectId: '项目ID',
            projectIdContent: '检查后将自动分配。',
            projectName: '项目名称',
            description: '描述',
            relatedLinks: '相关链接',
            save: '保存',
            backEdit: '返回编辑',
          },
          deleteLog: {
            type: '项目',
          },
          validation: {
            projectNameRequired: '请输入项目名称',
          },
          messages: {
            saving: '正在保存数据，请稍候...',
            saveSuccess: '项目已成功保存，您现在可以继续下一步！',
            deleteConfirm: '您确定要删除此项目吗？',
            deleteSuccess: '删除成功',
            unsavedConfirm: '您填写的数据尚未保存。您确定要跳过当前步骤吗？',
          },
        },
        common: {
          publications: {
            title: '文章信息',
            form: {
              journal: '期刊',
              doi: 'DOI',
              pmid: 'PMID',
              title: '标题',
              reference: '引用',
              autoFillTooltip: '点击自动填充。',
            },
            preview: {
              title: '预览',
              journal: '期刊',
              doi: 'DOI',
              pmid: 'PMID',
              articleTitle: '标题',
              reference: '引用',
              fillIn: '填入',
              cancel: '取消',
            },
            validation: {
              doiFormat: 'DOI格式不正确',
              pmidFormat: 'PMID格式不正确',
            },
            messages: {
              doiRequired: '请输入正确的DOI！',
              loading: '加载中...',
              notFound: '未找到文章信息！',
              fillSuccess: '文章信息填充成功！',
            },
          },
          relatedLinks: {
            label: '相关链接',
          },
          deleteLog: {
            title: '删除失败。目标删除数据已在以下资源中使用。',
            export: '导出',
            filePrefix: '删除失败日志',
            columns: {
              targetDeletionData: '目标删除数据',
              submissionNo: '提交编号',
              type: '类型',
              no: '编号',
              name: '名称',
            },
          },
        },
        experiment: {
          expSingle: {
            generalInfo: {
              title: '基本信息',
              experimentId: '实验ID',
              experimentIdPlaceholder: '检查后将自动分配。',
              experimentName: '实验名称',
              projectId: '项目ID',
              projectPlaceholder: '请选择项目',
              sampleDescription: '样本描述',
              experimentDescription: '实验描述',
              experimentProtocol: '实验协议',
            },
            experimentInfo: {
              title: '实验信息',
              description:
                '以下列出了常见的组学实验类型。如果您需要为您的提交添加其他组学类型，请发送邮件至{email}，简要描述您要提交的数据类型，我们的管理员将快速回复您。',
              experimentType: '实验类型',
            },
            buttons: {
              continue: '继续',
              previewSave: '预览并保存',
              reset: '重置',
              delete: '删除',
            },
            preview: {
              title: '预览',
              experimentTitle: '实验',
              experimentId: '实验ID',
              experimentIdContent: '检查后将自动分配。',
              experimentType: '实验类型',
              experimentName: '实验名称',
              projectId: '项目ID',
              description: '描述',
              experimentProtocol: '实验协议',
              relatedLinks: '相关链接',
              save: '保存',
              backEdit: '返回编辑',
            },
            deleteLog: {
              type: '实验',
            },
            validation: {
              experimentNameRequired: '请输入实验名称',
              projectRequired: '请选择项目',
            },
            messages: {
              insufficientData: '推荐属性数据不足，请继续填写',
              saving: '正在保存数据，请稍候...',
              deleteConfirm: '您确定要删除此实验吗？',
              deleteSuccess: '删除成功',
              saveSuccess: '实验已成功保存，您现在可以继续下一步！',
              antibodyInfo:
                '需要上传与实验相关的抗体信息表，将其放入创建的分析中，并与项目关联。',
              unsavedConfirm: '您填写的数据尚未保存。您确定要跳过当前步骤吗？',
            },
          },
          expMultiple: {
            title: '实验类型信息',
            description:
              '您可以分别通过单个和多个提交不同类型的实验信息。因此，您可以在一次提交中提交多达2种不同类型的实验。如果您需要提交更多种类的实验类型，您可以进行多次提交。',
            experimentType: '实验类型',
            buttons: {
              continue: '继续',
              checkSave: '检查并保存',
              reset: '重置',
              delete: '删除',
            },
            deleteLog: {
              type: '多个实验',
            },
            messages: {
              antibodyInfo:
                '需要上传与实验相关的抗体信息表，将其放入创建的分析中，并与项目关联。',
              unsavedConfirm: '您填写的数据尚未保存。您确定要跳过当前步骤吗？',
              saving: '正在保存数据，请稍候...',
              deleteConfirm: '您确定要删除所有多个实验吗？',
              deleteSuccess: '删除成功',
              invalidCells: '请更正所有无效单元格。',
              dataEmpty: '提交的数据不能为空',
              saveSuccess: '实验已成功保存，您现在可以继续下一步！',
            },
          },
        },
        sample: {
          sampleSingle: {
            sampleType: {
              title: '样本类型',
              collapse: '收起',
              more: '更多>>',
            },
            generalInfo: {
              title: '基本信息',
              sampleName: '样本名称',
              organism: '物种名称',
              organismPlaceholder: '请输入搜索分类',
              tissue: '组织',
              sampleDescription: '样本描述',
              sampleProcessingProtocol: '样本处理协议',
            },
            sampleInfo: {
              title: '样本信息',
            },
            buttons: {
              continue: '继续',
              previewSave: '预览并保存',
              reset: '重置',
              delete: '删除',
            },
            preview: {
              title: '预览',
              sampleTitle: '样本',
              sampleId: '样本ID',
              sampleIdContent: '检查后将自动分配。',
              sampleType: '样本类型',
              sampleName: '样本名称',
              description: '描述',
              processingProtocol: '处理协议',
              relatedLinks: '相关链接',
              save: '保存',
              backEdit: '返回编辑',
            },
            deleteLog: {
              type: '样本',
            },
            validation: {
              sampleNameRequired: '请输入样本名称',
            },
            messages: {
              insufficientData: '推荐属性数据不足，请继续填写',
              saving: '正在保存数据，请稍候...',
              saveSuccess: '样本已成功保存，您现在可以继续下一步！',
              deleteConfirm: '您确定要删除此样本吗？',
              deleteSuccess: '删除成功',
              unsavedConfirm: '您填写的数据尚未保存。您确定要跳过当前步骤吗？',
            },
          },
          sampleMultiple: {
            title: '样本类型和信息',
            description: '填写样本类型并点击保存后，您可以选择新类型继续上传',
            buttons: {
              continue: '继续',
              checkSave: '检查并保存',
              reset: '重置',
              delete: '删除',
            },
            deleteLog: {
              type: '多个样本',
            },
            messages: {
              addDataConfirm: '您确定要将选中的数据添加到表格中吗？',
              unsavedConfirm: '您填写的数据尚未保存。您确定要跳过当前步骤吗？',
              deleteConfirm: '您确定要删除所有多个样本吗？',
              deleteSuccess: '删除成功',
              saving: '正在保存数据，请稍候...',
              invalidCells: '请更正所有无效单元格。',
              dataEmpty: '提交的数据不能为空',
              saveSuccess: '样本已成功保存，您现在可以继续下一步！',
            },
          },
        },
        archivingSingle: {
          selectData: {
            title: '选择数据',
            unarchivedData: '未归档/归档中数据',
            archivedRawData: '已归档原始数据',
            archivedAnalysisData: '已归档分析数据',
          },
          archiving: {
            title: '归档',
            itemsSelected: '项已选择',
            clear: '清除',
          },
          form: {
            title: '选择归档原始数据',
            project: '项目',
            projectPlaceholder: '请选择项目',
            experiment: '实验',
            experimentPlaceholder: '请选择实验',
            sample: '样本',
            samplePlaceholder: '请选择样本',
            run: '批次',
            selectRun: '选择批次',
            runPlaceholder: '选择现有批次',
            createNewRun: '创建新批次',
            customRunName: '自定义批次名称',
            description: '描述',
          },
          buttons: {
            checkSave: '检查并保存',
            submit: '提交',
            reset: '重置',
          },
          validation: {
            runNameRequired: '请输入批次名称',
          },
          messages: {
            selectDataRequired: '请选择需要归档的数据',
            projectRequired: '请选择项目',
            experimentRequired: '请选择实验',
            sampleRequired: '请选择样本',
            runNameRequired: '请输入批次名称',
            runRequired: '请选择批次',
            saving: '正在保存数据，请稍候...',
            submissionIncomplete:
              '提交信息尚未完成。我们建议您按照页面左侧边栏的导航，从提交者详细信息开始，逐步填写信息，并随时保存。',
          },
        },
        archivingMultiple: {
          buttons: {
            checkSave: '检查并保存',
            reset: '重置',
            delete: '删除',
          },
          tabs: {
            unarchivedData: '未归档数据',
            archivingData: '归档中数据',
            archivedRawData: '已归档原始数据',
            archivedAnalysisData: '已归档分析数据',
          },
          columns: {
            experimentId: {
              title: '实验ID',
              description:
                '对于新创建的实验，实验ID可以留空。要为已成功创建且已有实验ID的实验归档数据，请在此处填写实验ID。',
            },
            experimentName: {
              title: '实验名称',
              description:
                '填写您计划创建的实验的唯一名称，或已成功创建并分配了实验ID的实验名称。如果实验ID和实验名称不匹配，以实验ID为准，实验名称将被忽略。',
            },
            sampleId: {
              title: '样本ID',
              description:
                '对于新创建的样本，样本ID可以留空。要为已成功创建且已有样本ID的样本归档数据，请在此处填写样本ID。',
            },
            sampleName: {
              title: '样本名称',
              description:
                '填写您计划创建的样本的唯一名称，或已成功创建并分配了样本ID的样本名称。如果样本ID和样本名称不匹配，以样本ID为准，样本名称将被忽略。',
            },
            runId: {
              title: '批次ID',
              description:
                '对于新创建的批次，批次ID可以留空。要为已成功创建且已有批次ID的批次归档原始数据，请在此处填写批次ID。如果实验ID和实验名称不匹配，以实验ID为准，实验名称将被忽略。',
            },
            runName: {
              title: '批次名称',
              description:
                '填写您计划创建的批次的唯一名称，或已成功创建并分配了批次ID的批次名称。如果批次ID和批次名称不匹配，将被视为对批次名称的更新。',
            },
            runDescription: {
              title: '批次描述',
              description: '批次的描述。',
            },
            dataId: {
              title: '数据ID',
              description:
                '填写要为原始数据归档的数据ID。对于双端测序数据，两个文件（正向和反向）是一个批次。对于单端测序数据，一个文件是一个批次。data_id列中填写的数据ID不能重复。',
            },
            fileName: {
              title: '文件名',
              description: '如果填写文件名，将更新数据的文件名。',
            },
            dataRemark: {
              title: '数据备注',
              description: '数据的备注。',
            },
          },
          messages: {
            saving: '正在保存数据，请稍候...',
            invalidCells: '请更正所有无效单元格。',
            deleteConfirm: '您确定要删除所有多个归档数据吗？',
            deleting: '正在删除，请稍候',
            deleteSuccess: '取消归档成功',
          },
        },
      },
      analysis: {
        analysisSingle: {
          generalInfo: {
            title: '基本信息',
            analysisId: '分析ID',
            analysisIdPlaceholder: '将自动生成。',
            analysisName: '分析名称',
            analysisDescription: '分析描述',
          },
          analysisType: {
            title: '分析类型',
            other: '其他',
          },
          target: {
            title: '目标',
          },
          pipeline: {
            title: '流程',
          },
          buttons: {
            continue: '继续',
            previewSave: '预览并保存',
            reset: '重置',
            delete: '删除',
          },
          preview: {
            title: '预览',
            analysisTitle: '分析',
            analysisId: '分析ID',
            analysisIdContent: '检查后将自动分配。',
            analysisName: '分析名称',
            description: '描述',
            analysisType: '分析类型',
            targetTitle: '目标',
            target: '目标',
            otherTarget: '其他目标',
            name: '名称',
            link: '链接',
            pipelineTitle: '流程',
            program: '程序',
            version: '版本',
            output: '输出',
            notes: '备注',
            save: '保存',
            backEdit: '返回编辑',
          },
          deleteLog: {
            type: '分析',
          },
          validation: {
            analysisNameRequired: '请输入分析名称',
          },
          messages: {
            analysisTypeRequired: '请选择分析类型',
            otherAnalysisTypeRequired: '请填写其他分析类型',
            otherAnalysisTypeNoChinese: '其他分析类型不能包含中文字符',
            targetRequired: '请填写目标或其他目标，请不要留空卡片',
            targetNoChinese: '目标不能包含中文字符',
            otherTargetRequired: '请填写其他目标名称和链接',
            otherTargetNoChinese: '其他目标源不能包含中文字符',
            pipelineRequired: '请填写流程，不要留空卡片',
            pipelineNameRequired: '请填写流程名称，名称是必需的',
            pipelineNoChinese: '流程不能包含中文字符',
            saving: '正在保存数据，请稍候...',
            saveSuccess: '分析已成功保存，您现在可以继续下一步！',
            deleteConfirm: '您确定要删除此分析吗？',
            deleteSuccess: '删除成功',
            unsavedConfirm: '您填写的数据尚未保存。您确定要跳过当前步骤吗？',
          },
        },
        analysisMultiple: {
          buttons: {
            continue: '继续',
            checkSave: '检查并保存',
            reset: '重置',
            delete: '删除',
          },
          deleteLog: {
            type: '多个分析',
          },
          resultLog: {
            type: '分析',
          },
          columns: {
            analysisId: {
              title: '分析ID',
              description:
                '对于新创建的分析，分析ID可以留空。修改已分配分析ID的分析时，必须填写分析ID。分析ID一旦分配，不能更改。',
            },
            analysisName: {
              title: '分析名称',
              description:
                '填写您计划创建的分析的唯一名称，或已成功创建并分配了分析ID的分析名称。如果分析ID和分析名称不匹配，将被视为对分析名称的更新。',
            },
            description: {
              title: '描述',
              description: '分析的描述。',
            },
            analysisType: {
              title: '分析类型',
              description:
                '分析的类型。如果您使用的分析类型不在选择列表中，您可以选择"其他"并在other_analysis_type列中填写分析类型。',
              other: '其他',
            },
            otherAnalysisType: {
              title: '其他分析类型',
              description:
                '如果analysis_type是"其他"，请在此列中填写分析类型名称。',
            },
            index: {
              title: '索引',
              description:
                '填写分析步骤的序号。例如，如果分析过程有三个步骤，索引应该是1、2、3。',
            },
            program: {
              title: '程序',
              description: '每个分析步骤中使用的工具。',
            },
            link: {
              title: '链接',
              description: '每个分析步骤中使用的工具的相关链接。',
            },
            version: {
              title: '版本',
              description: '每个分析步骤中使用的工具的版本。',
            },
            note: {
              title: '备注',
              description: '分析步骤的备注。',
            },
            outputFile: {
              title: '输出文件',
              description: '分析步骤生成的输出文件。',
            },
            targetProject: {
              title: '目标项目',
              description:
                '如果分析与项目相关，请填写现有的项目ID。多个关联用分号分隔，例如OEP000001;OEP000002;OEP000003。',
            },
            targetExperiment: {
              title: '目标实验',
              description:
                '如果分析与实验相关，请填写现有的实验ID。多个关联用分号分隔，例如OEX000001;OEX000002;OEX000003。',
            },
            targetSample: {
              title: '目标样本',
              description:
                '如果分析与样本相关，请填写现有的样本ID。多个关联用分号分隔，例如OES000001;OES000002;OES000003。',
            },
            targetAnalysis: {
              title: '目标分析',
              description:
                '如果分析与另一个分析相关，请填写关联的分析ID。多个关联用分号分隔，例如OEZ000001;OEZ000002;OEZ000003。',
            },
            targetRun: {
              title: '目标批次',
              description:
                '如果分析与批次相关，请填写现有的批次ID。多个关联用分号分隔，例如OER000001;OER000002;OER000003。',
            },
            targetData: {
              title: '目标数据',
              description:
                '如果分析与数据相关，请填写现有的数据ID。多个关联用分号分隔，例如OED000001;OED000002;OED000003。',
            },
            targetOtherName: {
              title: '其他目标名称',
              description:
                '如果分析与来自其他来源的目标相关，请填写目标的名称。多个关联用分号分隔，例如SRA;GSA。',
            },
            targetOtherLink: {
              title: '其他目标链接',
              description:
                '如果分析与来自其他来源的目标相关，请填写目标的链接。多个关联用分号分隔。',
            },
          },
          messages: {
            unsavedConfirm: '您填写的数据尚未保存。您确定要跳过当前步骤吗？',
            saving: '正在保存数据，请稍候...',
            invalidCells: '请更正所有无效单元格。',
            saveSuccess: '分析已成功保存，您现在可以继续下一步！',
            deleteConfirm: '您确定要删除所有多个分析吗？',
            deleteSuccess: '删除成功',
          },
        },
        archivingSingle: {
          selectData: {
            title: '选择数据',
            unarchivedData: '未归档/归档中数据',
            archivedAnalysisData: '已归档分析数据',
            archivedRawData: '已归档原始数据',
          },
          archiving: {
            title: '归档',
            itemsSelected: '项已选择',
            clear: '清除',
          },
          form: {
            title: '选择归档分析',
            analysis: '分析',
            analysisPlaceholder: '请选择分析',
          },
          buttons: {
            checkSave: '检查并保存',
            submit: '提交',
            reset: '重置',
          },
          messages: {
            selectDataRequired: '请选择需要归档的数据',
            analysisRequired: '请选择分析',
            saving: '正在保存数据，请稍候...',
            submissionIncomplete:
              '提交信息尚未完成。我们建议您按照页面左侧边栏的导航，从提交者详细信息开始，逐步填写信息，并随时保存。',
          },
        },
        archivingMultiple: {
          buttons: {
            checkSave: '检查并保存',
            reset: '重置',
            delete: '删除',
          },
          tabs: {
            unarchivedData: '未归档数据',
            archivingData: '归档中数据',
            archivedAnalysisData: '已归档分析数据',
            archivedRawData: '已归档原始数据',
          },
          resultLog: {
            type: '归档-分析',
          },
          columns: {
            analysisId: {
              title: '分析ID',
              description:
                '对于新创建的分析，分析ID可以留空。要为已成功创建且已有分析ID的分析归档数据，请在此处填写分析ID。',
            },
            analysisName: {
              title: '分析名称',
              description:
                '填写您计划创建的分析的唯一名称，或已成功创建并分配了分析ID的分析名称。如果分析ID和分析名称不匹配，以分析ID为准，分析名称将被忽略。',
            },
            dataId: {
              title: '数据ID',
              description:
                '填写要为分析归档的数据ID。data_id列中填写的数据ID不能重复。',
            },
            fileName: {
              title: '文件名',
              description: '如果填写文件名，将更新数据的文件名。',
            },
            dataRemark: {
              title: '数据备注',
              description: '数据的备注。',
            },
          },
          messages: {
            saving: '正在保存数据，请稍候...',
            invalidCells: '请更正所有无效单元格。',
            deleteConfirm: '您确定要删除所有多个归档数据吗？',
            deleting: '正在删除，请稍候',
            deleteSuccess: '取消归档成功',
          },
        },
        index: {
          breadcrumb: '元数据',
          submitter: '提交者',
          analysis: '分析',
          multiple: '批量',
          single: '单个',
          archiving: '归档',
        },
      },
    },
    components: {
      fillTip: {
        required: '必填信息',
        recommend: '推荐信息',
      },
      archivingDialog: {
        defaultMessage: '归档已完成。',
        submitInstruction: '您可以点击<strong>提交</strong>按钮来提交您的数据',
        viewResultsPrefix: '您也可以在',
        submissionPageLink: '我的提交页面',
        submit: '提交',
        backEdit: '返回编辑',
        messages: {
          submissionNoEmpty: '提交编号不能为空',
          confirmSubmit: '确认提交 {subNo} 的数据？',
          submitting: '正在提交，请稍候',
          error: '错误',
        },
      },
      chooseData: {
        uploadData: '上传数据',
        metaData: '元数据',
        archiving: '归档',
      },
      mySubmissionList: {
        searchPlaceholder: '搜索 [ 提交ID ]',
        createTime: '创建时间',
        dateTo: '至',
        startDate: '开始日期',
        endDate: '结束日期',
        search: '搜索',
        status: '状态',
        statusOptions: {
          all: '全部',
          editing: '编辑中',
          waitingReview: '等待审核',
          reviewing: '审核中',
          complete: '完成',
          rejected: '已拒绝',
          deleted: '已删除',
        },
        statusText: {
          revoke: '撤回',
          editing: '编辑中',
          reviewing: '审核中',
          waitingReview: '等待审核',
          processing: '处理中',
          deleted: '已删除',
          complete: '完成',
          rejected: '已拒绝',
        },
        columns: {
          submissionId: '提交ID',
          dataEntries: '数据条目数',
          submitter: '提交者',
          status: '状态',
          createTime: '创建时间',
          lastModified: '最后修改',
          operate: '操作',
        },
        operations: {
          edit: '编辑',
          submit: '提交',
          delete: '删除',
          revoke: '撤回',
        },
        rejectReason: {
          title: '拒绝原因',
          reasonType: '原因类型',
          details: '详情',
        },
        noData: '无数据',
        contactMessage: '如果您需要查询历史提交数据，请联系',
        messages: {
          deleteConfirm:
            '确认删除提交：{subNo}？<br>删除后数据无法恢复，请谨慎操作。<br><span class="text-danger">请耐心等待删除完全完成。中断过程可能会导致后续操作出错。</span>',
          deleting: '正在删除，请稍候',
          deleteSuccess: '删除成功',
          submitConfirm: '确认提交 {subNo} 的数据？',
          submitting: '正在提交，请稍候',
          submitSuccess: '提交成功',
          revokeConfirm: '确认撤回提交：{subNo}？',
          revoking: '正在撤回，请稍候',
          revokeSuccess: '撤回成功',
          error: '错误',
        },
      },
    },
    route1: {
      title1: 'FTP 工具上传',
      step1: {
        part1: '1. 批次 FTP 工具，例如',
        tools: 'Filezilla, WinsCP',
        filezilla: 'Filezilla',
        winscp: 'WinsCP',
      },
      step2: '2. 在加密选项中选择“SFTP - SSH 文件传输协议”。',
      step3: {
        part1: '3. 输入：主机名',
        part2: '端口',
      },
      step4: '4. 输入：用户名（NODE 用户名），密码（NODE 密码）。',
      step5: '5. 将本地文件放到远程 FTP 目录中进行传输。',
      title2: '命令行上传',
      popoverTitle: '使用以下凭据建立 FTP 连接：',
      command: {
        ftpEnter: '输入：',
        password: '密码：[您的 NODE 密码]',
        mkdir: '创建一个有意义名称的子文件夹（必需）',
        cd: '导航到您刚创建的目标文件夹',
        put: '将文件复制到目标文件夹',
        ls: '查看当前目录中的数据',
      },
      note1:
        '请为每次新提交创建新的子目录，该子目录用于临时存放待提交的数据，并在整个提交完成之后删除。请不要创建或使用复杂的目录结构或上传不包含测序数据的文件。',
      note2: 'FTP上传方式对文件大小没有限制',
      note3:
        '请使用正确的SFTP地址、端口号，NODE用户名和密码创建自己的FTP连接。',
      note4:
        '文件路径和名称中不要包含空格、&、%、*、希腊字母等特殊字符。建议使用大写字母（A-Z）、小写字母（a-z）、数字（0-9）、下划线（_）和连字符（-）组合构造文件名。',
    },
    route2: {
      selectFile: '选择您的文件',
      sizeLimit: '大小限制为200MB',
      acceptFile: '这里可以直接上传数据文件。',
      forbiddenTypes: '不支持上传的文件类型包括： {blacklist}',
      sizeLimitNote: '文件大小在200MB以内',
    },
    route3: {
      expressName: '4快递机构名称',
      trackingNum: '快递单号',
      filePath: '文件路径',
      filePathPlaceholder: '上传到硬盘的路径',
      note1: '大规模数据提交可以采用硬盘邮寄的方式。',
      note2:
        '您可以选择可靠的快递方式寄送硬盘、存储卡、U盘或其他包含数据的介质。',
      note3: '我们会在完成数据上传之后将设备返还给您。',
      contact: '邮寄前请先联系我们：',
      submitButton: '提交',
    },
  },
  home: {
    title: {
      node: 'NODE:',
      subtitle: '多组学数据汇交平台',
    },
    search: {
      placeholder: '搜索',
      advancedSearch: '高级搜索',
      example: '例如',
    },
    cards: {
      dataSubmission: '数据提交',
      dataBrowsing: '数据浏览',
      dataStatistics: '数据统计',
      dataDownload: '数据下载',
      helpDocuments: '帮助文档',
    },
    contact: {
      title: '联系我们',
      phone: '+86-21-********',
      wechatText: '欢迎联系 {email} 加入NODE微信群。',
      wechatAccount: '微信公众号',
      address: {
        center: '生物医学大数据中心',
        institute: '中国科学院上海营养与健康研究所',
        academy: '中国科学院',
        street: '岳阳路320号',
        city: '上海 200031，中国',
      },
    },
    featuredData: {
      title: '特色数据',
      humanResource: '人类资源',
      microbeResource: '微生物资源',
      omicsResource: '组学资源',
      more: '更多>>',
      projects: '项目',
      experiments: '实验',
    },
    cite: {
      title: '如何引用',
      description:
        '成功向NODE提交数据后，我们建议在您的手稿中使用以下措辞来描述数据存储：',
      content:
        '所有数据均可在NODE ({originUrl}) 中通过登录号XXX（例如，OEP00000073）或通过URL访问：{hrefUrl}',
      publicationText: '请引用以下文章信息：',
      publicationTitle: '多组学大数据共享平台研究进展。',
      publicationJournal: '生命科学通报。',
      publicationDetails: '2023, 35(12): 1553-1560. DOI:',
    },
    tools: {
      title: '工具',
    },
    latestData: {
      title: '最新文章',
      more: '更多>>',
    },
    publications: {
      title: '文章信息',
      totalNumber: '总数量：{count}',
    },
    hydrosphere: '水圈',
    copySuccess: '复制成功',
  },
  unauthorized: {
    title: '无访问权限',
  },
  download: {
    title: '数据下载',
    publicDownloadServer: '公共下载服务器',
    downloadAboutToStart: '下载即将开始',
    rawData: '原始数据',
    analysisData: '分析数据',
    searchPlaceholder: '搜索 [ID] 或 [名称]',
    search: '搜索',
    operate: '操作',
    exportDataLinks: '导出数据链接',
    pleaseSelectDataFirst: '请先选择数据',
    tooltips: {
      htmlDownload: 'html下载',
      sftpDownload: 'sftp下载',
    },
    columns: {
      projectId: '项目ID',
      projectName: '项目名称',
      experimentId: '实验ID',
      experimentName: '实验名称',
      experimentType: '实验类型',
      experimentDescription: '实验描述',
      sampleId: '样本ID',
      sampleName: '样本名称',
      sampleType: '样本类型',
      sampleDescription: '样本描述',
      runId: '批次ID',
      runName: '批次名称',
      dataId: '数据ID',
      dataName: '数据名称',
      dataSize: '数据大小',
      dataUploadTime: '数据上传时间',
      analysisId: '分析ID',
      analysisName: '分析名称',
      analysisType: '分析类型',
      dataType: '数据类型',
    },
    dialogs: {
      ftpDownload: {
        title: 'FTP下载',
        tips: '提示：',
        accountTip: '您也可以使用自己的NODE账户和密码来下载数据',
        ftpAddress: 'FTP地址：',
        ftpFilepath: 'FTP文件路径：',
        copyPath: '复制路径',
        copySuccess: '复制成功',
      },
      httpDownload: {
        title: 'HTTP下载',
        downloadLink: '下载链接：',
        localDownload: '本地下载',
        fileSizeExceedsLimit: '文件大小超过200MB，不允许通过HTTP下载',
      },
    },
  },
  statistic: {
    title: '数据统计',
    navigation: {
      dataVolume: '数据量',
      popularData: '热门数据',
      publication: '文章信息',
    },
    dataVolume: {
      metadata: '元数据',
      project: '项目',
      experiment: '实验',
      sample: '样本',
      analysis: '分析',
      rawData: '原始数据',
      analysisData: '分析数据',
      dataFlow: '数据流',
      accessible: '可访问：',
      unaccessible: '不可访问：',
      items: '项',
      number: '数量：',
      size: '大小：',
    },
    popularData: {
      title: '热门数据',
      visits: '访问量',
      download: '下载量',
      requested: '数据申请量',
    },
    publication: {
      title: '文章信息',
    },
  },
  common: {
    home: '首页',
    submit: '提交',
    columnVisibility: '列可见性',
    search: '搜索',
    selectAll: '全选',
    confirm: '确认',
    default: '默认',
    total: '总计',
  },
  userCenter: {
    index: {
      breadcrumb: '用户中心',
      notice: {
        title: '通知',
        message: '您的密码已修改超过90天。为了您的账户安全，请点击',
        here: '这里',
        suffix: '及时更新您的密码',
      },
      tabs: {
        myDataList: '我的数据列表',
        myDataStatistics: '我的数据统计',
        myDataActivity: '我的数据被访统计',
      },
      dataList: {
        projects: '项目',
        experiments: '实验',
        samples: '样本',
        runs: '批次',
        analysis: '分析',
        data: '数据',
        publishes: '文章',
        submissions: '提交',
      },
    },
    userLeft: {
      tooltips: {
        edit: '编辑',
        changePassword: '修改密码',
      },
      accountStatus: {
        approved: '账户状态：已审核',
        awaitingReview: '账户状态：等待审核',
        notApproved: '账户状态：未审核',
      },
      userInfo: {
        name: '姓名',
        organization: '组织',
      },
      sections: {
        share: '共享',
        requests: '数据申请',
        reviews: '审核',
      },
      links: {
        myShares: '我的共享',
        sharesFromOthers: '来自他人的共享',
        myRequests: '我的申请',
        requestsFromOthers: '来自他人的申请',
        myReviews: '我的申请',
      },
    },
    data: {
      analysis: {
        searchPlaceholder: '搜索 [ID 或 名称]',
        period: '上传时间',
        dateTo: '至',
        startDate: '开始日期',
        endDate: '结束日期',
        search: '搜索',
        other: '其他',
        columns: {
          id: 'ID',
          name: '名称',
          analysisType: '分析类型',
          dataNumber: '数据数量',
          description: '描述',
          uploadDate: '上传日期',
          status: '状态',
          operate: '操作',
        },
        status: {
          accessible: '可访问',
          unaccessible: '不可访问',
        },
        operations: {
          edit: '编辑',
          delete: '删除',
        },
        batchModifyTooltip:
          "批量修改：<a href='{url}/submit/metadata/analysisData'>{url}/submit/metadata/analysisData</a>",
        deleteLog: {
          type: '分析',
        },
        messages: {
          openingDeleteDialog: '正在打开删除对话框，请稍候',
          verifyingAndDeleting: '正在验证密码并删除，请稍候',
        },
      },
      data: {
        tabs: {
          unarchivedData: '未归档数据',
          archivedRawData: '已归档原始数据',
          archivedAnalysisData: '已归档分析数据',
        },
      },
      experiment: {
        searchPlaceholder: '搜索 [ID 或 名称]',
        period: '上传日期',
        dateTo: '至',
        startDate: '开始日期',
        endDate: '结束日期',
        search: '搜索',
        experimentType: '实验类型：',
        columns: {
          id: 'ID',
          name: '名称',
          experimentType: '实验类型',
          sampleType: '样本类型',
          description: '描述',
          samples: '样本',
          uploadDate: '上传日期',
          status: '状态',
          operate: '操作',
        },
        status: {
          accessible: '可访问',
          unaccessible: '不可访问',
        },
        operations: {
          edit: '编辑',
          delete: '删除',
        },
        batchModifyTooltip:
          "批量修改：<a href='{url}/submit/metadata/rawData'>{url}/submit/metadata/rawData</a>",
        deleteLog: {
          type: '实验',
        },
        messages: {
          openingDeleteDialog: '正在打开删除对话框，请稍候',
          verifyingAndDeleting: '正在验证密码并删除，请稍候',
        },
      },
      project: {
        searchPlaceholder: '搜索 [ID 或 名称]',
        period: '上传日期',
        dateTo: '至',
        startDate: '开始日期',
        endDate: '结束日期',
        search: '搜索',
        columns: {
          id: 'ID',
          name: '名称',
          experimentType: '实验类型',
          sampleType: '样本类型',
          description: '描述',
          uploadDate: '上传日期',
          status: '状态',
          operate: '操作',
        },
        status: {
          accessible: '可访问',
          unaccessible: '不可访问',
        },
        operations: {
          edit: '编辑',
          delete: '删除',
        },
        deleteLog: {
          type: '项目',
        },
        messages: {
          openingDeleteDialog: '正在打开删除对话框，请稍候',
          verifyingAndDeleting: '正在验证密码并删除，请稍候',
        },
      },
      publish: {
        searchPlaceholder: '搜索 [标题、期刊、DOI、PMID 或 相关项目]',
        period: '创建日期',
        dateTo: '至',
        startDate: '开始日期',
        endDate: '结束日期',
        search: '搜索',
        columns: {
          title: '标题',
          journal: '期刊',
          doi: 'DOI',
          pmid: 'PMID',
          relatedItems: '相关项目',
          reference: '引用',
          createDate: '创建日期',
          operate: '操作',
        },
        operations: {
          edit: '编辑',
          delete: '删除',
        },
        add: '添加',
        messages: {
          deleteConfirm: '您确定要删除此发布吗？',
          deleting: '正在删除',
        },
      },
      run: {
        searchPlaceholder: '搜索 [ID 或 名称]',
        period: '上传日期',
        dateTo: '至',
        startDate: '开始日期',
        endDate: '结束日期',
        search: '搜索',
        columns: {
          id: 'ID',
          name: '名称',
          experimentId: '实验ID',
          sampleId: '样本ID',
          dataNumber: '数据数量',
          description: '描述',
          uploadDate: '上传日期',
          status: '状态',
          operate: '操作',
        },
        status: {
          accessible: '可访问',
          unaccessible: '不可访问',
        },
        operations: {
          delete: '删除',
        },
        batchModifyTooltip:
          "批量修改：<a href='{url}/submit/metadata/rawData'>{url}/submit/metadata/rawData</a>",
        deleteLog: {
          type: '批次',
        },
        messages: {
          openingDeleteDialog: '正在打开删除对话框，请稍候',
          verifyingAndDeleting: '正在验证密码并删除，请稍候',
        },
      },
      sample: {
        searchPlaceholder: '搜索 [ID 或 名称]',
        organism: '物种名称',
        select: '选择',
        period: '上传日期',
        dateTo: '至',
        startDate: '开始日期',
        endDate: '结束日期',
        search: '搜索',
        sampleType: '样本类型：',
        columns: {
          id: 'ID',
          name: '名称',
          sampleType: '样本类型',
          organism: '物种名称',
          experimentType: '实验类型',
          description: '描述',
          uploadDate: '上传日期',
          status: '状态',
          operate: '操作',
        },
        status: {
          accessible: '可访问',
          unaccessible: '不可访问',
        },
        operations: {
          edit: '编辑',
          delete: '删除',
        },
        batchModifyTooltip:
          "批量修改：<a href='{url}/submit/metadata/rawData'>{url}/submit/metadata/rawData</a>",
        deleteLog: {
          type: '样本',
        },
        messages: {
          openingDeleteDialog: '正在打开删除对话框，请稍候',
          verifyingAndDeleting: '正在验证密码并删除，请稍候',
        },
      },
      components: {
        archivedAnalysisData: {
          searchPlaceholder: '搜索 [ID 或 文件名]',
          period: '上传日期',
          dateTo: '至',
          startDate: '开始日期',
          endDate: '结束日期',
          search: '搜索',
          columns: {
            dataId: '数据ID',
            analysisId: '分析ID',
            analysisName: '分析名称',
            analysisType: '分析类型',
            fileName: '文件名',
            security: '安全性',
            size: '大小',
            dataType: '数据类型',
            uploadDate: '上传日期',
            operate: '操作',
          },
          operations: {
            htmlDownload: 'html下载',
            sftpDownload: 'sftp下载',
            delete: '删除',
          },
          batchModifyTooltip:
            "批量修改：<a href='{url}/submit/metadata/analysisData'>{url}/submit/metadata/analysisData</a>",
          deleteLog: {
            type: '数据',
          },
          messages: {
            openingDeleteDialog: '正在打开删除对话框，请稍候',
            verifyingAndDeleting: '正在验证密码并删除，请稍候',
          },
        },
        archivedRawData: {
          searchPlaceholder: '搜索 [ID 或 文件名]',
          period: '上传日期',
          dateTo: '至',
          startDate: '开始日期',
          endDate: '结束日期',
          search: '搜索',
          columns: {
            dataId: '数据ID',
            fileName: '文件名',
            project: '项目',
            experiment: '实验',
            sample: '样本',
            security: '安全性',
            size: '大小',
            dataType: '数据类型',
            uploadDate: '上传日期',
            operate: '操作',
          },
          operations: {
            htmlDownload: 'html下载',
            sftpDownload: 'sftp下载',
            delete: '删除',
          },
          batchModifyTooltip:
            "批量修改：<a href='{url}/submit/metadata/rawData'>{url}/submit/metadata/rawData</a>",
          deleteLog: {
            type: '数据',
          },
          messages: {
            openingDeleteDialog: '正在打开删除对话框，请稍候',
            verifyingAndDeleting: '正在验证密码并删除，请稍候',
          },
        },
        unarchivedData: {
          searchPlaceholder: '搜索 [ID 或 文件名]',
          period: '上传日期',
          dateTo: '至',
          startDate: '开始日期',
          endDate: '结束日期',
          search: '搜索',
          columns: {
            dataId: '数据ID',
            fileName: '文件名',
            dataType: '数据类型',
            uploadType: '上传类型',
            uploadDate: '上传日期',
            size: '大小',
            operate: '操作',
          },
          operations: {
            delete: '删除',
          },
          export: '导出',
          deleteLog: {
            type: '数据',
          },
          messages: {
            openingDeleteDialog: '正在打开删除对话框，请稍候',
            verifyingAndDeleting: '正在验证密码并删除，请稍候',
            exportAllConfirm: '将导出所有未归档表格数据',
            exportSelectedConfirm: '将导出选中的未归档表格数据',
          },
        },
        deleteConfirm: {
          title: '删除',
          noticeTitle: '注意：以下项目将被删除',
          shareWarning: '以下数据正在被共享。删除后，相关共享将失效',
          shareLabel: '共享',
          passwordPlaceholder: '请验证您的密码',
          confirm: '确认',
        },
      },
    },
    dataActivity: {
      tabs: {
        viewsOfData: '数据查看',
        downloadOfData: '数据下载',
      },
      country: '国家：',
      select: '选择',
      chart: {
        loading: '加载中',
        legend: {
          projects: '项目',
          experiments: '实验',
          samples: '样本',
          analysis: '分析',
          data: '数据',
          totalData: '总数据',
        },
      },
    },
    edit: {
      analysis: {
        analysisSingle: {
          generalInfo: {
            title: '基本信息',
            analysisId: '分析ID',
            analysisIdPlaceholder: '将自动生成。',
            analysisName: '分析名称',
            analysisDescription: '分析描述',
          },
          analysisType: {
            title: '分析类型',
            other: '其他',
          },
          target: {
            title: '目标',
          },
          pipeline: {
            title: '流程',
          },
          buttons: {
            previewSave: '预览并保存',
            reset: '重置',
          },
          preview: {
            title: '预览',
            analysisTitle: '分析',
            analysisId: '分析ID',
            analysisIdContent: '检查后将自动分配。',
            analysisName: '分析名称',
            description: '描述',
            analysisType: '分析类型',
            targetTitle: '目标',
            target: '目标',
            otherTarget: '其他目标',
            name: '名称：',
            link: '链接：',
            pipelineTitle: '流程',
            program: '程序',
            version: '版本',
            output: '输出',
            notes: '备注',
            save: '保存',
            backEdit: '返回编辑',
          },
          validation: {
            analysisNameRequired: '请输入分析名称',
          },
          messages: {
            editingCompleted: '分析编辑已完成',
            unsavedConfirm: '您填写的数据尚未保存。您确定要跳过当前步骤吗？',
            analysisTypeRequired: '请选择分析类型',
            otherAnalysisTypeRequired: '请填写其他分析类型',
            otherAnalysisTypeNoChinese: '其他分析类型不能包含中文字符',
            targetRequired: '请填写目标或其他目标，请不要留空卡片',
            targetNoChinese: '目标不能包含中文字符',
            otherTargetRequired: '请填写其他目标名称和链接',
            otherTargetNoChinese: '其他目标源不能包含中文字符',
            pipelineRequired: '请填写流程，不要留空卡片',
            pipelineNameRequired: '请填写流程名称，名称是必需的',
            pipelineNoChinese: '流程不能包含中文字符',
            saving: '正在保存数据，请稍候...',
            saveSuccess: '保存成功',
            deleteConfirm: '您确定要删除此分析吗？',
            deleteSuccess: '删除成功',
          },
        },
      },
      experiment: {
        expSingle: {
          generalInfo: {
            title: '基本信息',
            experimentId: '实验ID',
            experimentIdPlaceholder: '检查后将自动分配。',
            experimentName: '实验名称',
            projectId: '项目ID',
            projectPlaceholder: '请选择项目',
            sampleDescription: '样本描述',
            experimentDescription: '实验描述',
            experimentProtocol: '实验协议',
          },
          experimentInfo: {
            title: '实验信息',
            description:
              '以下列出了常见的组学实验类型。如果您需要为您的提交添加其他组学类型，请发送邮件至 <a class="text-primary" href="mailto:{email}">{email}</a>，简要描述您要提交的数据类型，我们的管理员将快速回复您。',
            experimentType: '实验类型',
          },
          buttons: {
            previewSave: '预览并保存',
            reset: '重置',
          },
          preview: {
            title: '预览',
            experimentTitle: '实验',
            experimentId: '实验ID',
            experimentType: '实验类型',
            experimentName: '实验名称',
            projectId: '项目ID',
            description: '描述',
            experimentProtocol: '实验协议',
            relatedLinks: '相关链接',
            save: '保存',
            backEdit: '返回编辑',
          },
          validation: {
            experimentNameRequired: '请输入实验名称',
            projectRequired: '请选择项目',
          },
          messages: {
            editingCompleted: '实验编辑已完成',
            insufficientData: '推荐属性数据不足，请继续填写',
            saving: '正在保存数据，请稍候...',
            deleteConfirm: '您确定要删除此实验吗？',
            deleteSuccess: '删除成功',
            saveSuccess: '保存成功',
            unsavedConfirm: '您填写的数据尚未保存。您确定要跳过当前步骤吗？',
          },
        },
      },
      project: {
        generalInfo: {
          title: '基本信息',
          projectId: '项目ID',
          projectIdPlaceholder: '检查后将自动分配。',
          projectName: '项目名称',
          projectDescription: '项目描述',
        },
        buttons: {
          previewSave: '预览并保存',
          reset: '重置',
        },
        preview: {
          title: '预览',
          projectTitle: '项目',
          projectId: '项目ID',
          projectName: '项目名称',
          description: '描述',
          relatedLinks: '相关链接',
          save: '保存',
          backEdit: '返回编辑',
        },
        validation: {
          projectNameRequired: '请输入项目名称',
        },
        messages: {
          editingCompleted: '项目编辑已完成',
          saving: '正在保存数据，请稍候...',
          saveSuccess: '保存成功',
          deleteConfirm: '您确定要删除此项目吗？',
          deleteSuccess: '删除成功',
          unsavedConfirm: '您填写的数据尚未保存。您确定要跳过当前步骤吗？',
        },
      },
      sample: {
        sampleSingle: {
          sampleType: {
            title: '样本类型',
            collapse: '收起',
            more: '更多>>',
          },
          generalInfo: {
            title: '基本信息',
            sampleId: '样本ID',
            sampleIdPlaceholder: '检查后将自动分配。',
            sampleName: '样本名称',
            organism: '物种名称',
            organismPlaceholder: '请输入搜索分类',
            tissue: '组织',
            sampleDescription: '样本描述',
            sampleProcessingProtocol: '样本处理协议',
          },
          sampleInfo: {
            title: '样本信息',
          },
          buttons: {
            previewSave: '预览并保存',
            reset: '重置',
          },
          preview: {
            title: '预览',
            sampleTitle: '样本',
            sampleId: '样本ID',
            sampleType: '样本类型',
            sampleName: '样本名称',
            description: '描述',
            processingProtocol: '处理协议',
            relatedLinks: '相关链接',
            save: '保存',
            backEdit: '返回编辑',
          },
          validation: {
            sampleNameRequired: '请输入样本名称',
          },
          messages: {
            editingCompleted: '样本编辑已完成',
            insufficientData: '推荐属性数据不足，请继续填写',
            saving: '正在保存数据，请稍候...',
            saveSuccess: '保存成功',
            deleteConfirm: '您确定要删除此样本吗？',
            deleteSuccess: '删除成功',
            unsavedConfirm: '您填写的数据尚未保存。您确定要跳过当前步骤吗？',
          },
        },
      },
    },
    personal: {
      myRequest: {
        title: {
          myRequests: '我的申请访问',
          requestFromOthers: '来自他人的申请访问',
        },
        filters: {
          sort: '排序',
          resourceType: '资源类型',
          year: '年份',
          select: '选择',
          before: '之前',
        },
        sort: {
          requestDate: '申请日期',
          mainId: '主ID',
          status: '状态',
        },
        resourceTypes: {
          project: '项目',
          experiment: '实验',
          sample: '样本',
          analysis: '分析',
        },
        status: {
          new: '新',
        },
        actions: {
          dataList: '数据列表',
          authorized: '已授权',
          declined: '已拒绝',
        },
        table: {
          dataId: '数据ID',
          name: '名称',
          experiment: '实验',
          sample: '样本',
          run: '批次',
          analysis: '分析',
        },
        dates: {
          expiryDate: '到期日期',
          requestDate: '申请日期',
          authorizationDate: '授权日期',
        },
        info: {
          owner: '所有者',
          requestor: '申请人',
        },
        content: {
          requestText: '申请理由',
          replyText: '回复信息',
        },
        dialog: {
          comment: '评论',
          save: '保存',
          cancel: '取消',
        },
        messages: {
          saving: '保存中',
        },
      },
      myReview: {
        title: '我的审核',
        filters: {
          sort: '排序',
          resourceType: '资源类型',
          year: '年份',
          select: '选择',
          before: '之前',
        },
        sort: {
          reviewDate: '审核日期',
          reviewId: '审核ID',
          status: '状态',
        },
        resourceTypes: {
          project: '项目',
          experiment: '实验',
          sample: '样本',
          analysis: '分析',
        },
        status: {
          review: '审核',
          new: '新',
          reviewing: '审核中',
          canceled: '已取消',
        },
        actions: {
          cancel: '取消',
          extension: '延期',
          exportDataLinks: '导出数据链接',
          dataIdList: '数据ID列表',
          copy: '复制',
        },
        counts: {
          projectCounts: '项目数量',
          analysisCounts: '分析数量',
          experimentCounts: '实验数量',
          sampleCounts: '样本数量',
          runCounts: '批次数量',
          dataCounts: '数据数量',
        },
        info: {
          entrance: '入口',
          reviewToEmail: '审核邮箱',
        },
        dates: {
          reviewDate: '审核日期',
          expiredDate: '过期日期',
        },
        dialog: {
          cancelConfirm: '您确定要取消审核吗？',
          extension: '延期',
          extensionTips: {
            tip1: '1. 审核URL将在有效期后失效。',
            tip2: '2. 我们建议在有效期后将安全性设置为公开。',
            tip3: '3. 我们建议将有效期设置为3个月以上。',
          },
          validPeriod: '有效期',
          pickDay: '选择日期',
          confirm: '确认',
          cancel: '取消',
        },
        messages: {
          cancelSuccess: '取消成功',
          extensionSuccess: '延期成功',
          pickDayRequired: '请选择日期',
          copySuccess: '复制成功',
        },
      },
      myShare: {
        title: '我的共享',
        filters: {
          sort: '排序',
          resourceType: '资源类型',
          year: '年份',
          select: '选择',
          before: '之前',
        },
        sort: {
          shareDate: '共享日期',
          shareId: '共享ID',
          status: '状态',
        },
        resourceTypes: {
          project: '项目',
          experiment: '实验',
          sample: '样本',
          analysis: '分析',
        },
        status: {
          share: '共享',
          new: '新',
          sharing: '共享中',
          canceled: '已取消',
        },
        actions: {
          cancel: '取消',
          exportDataLinks: '导出数据链接',
          dataList: '数据列表',
        },
        counts: {
          projectCounts: '项目数量',
          analysisCounts: '分析数量',
          experimentCounts: '实验数量',
          sampleCounts: '样本数量',
          runCounts: '批次数量',
          dataCounts: '数据数量',
        },
        dates: {
          shareDate: '共享日期',
        },
        info: {
          shareToEmail: '共享给邮箱',
          shareFromEmail: '共享来源邮箱',
        },
        dialog: {
          cancelConfirm: '您确定要取消共享吗？',
          confirm: '确认',
          cancel: '取消',
        },
        messages: {
          cancelSuccess: '取消成功',
        },
      },
      reviewDetail: {
        breadcrumb: '审核详情',
        title: '审核详情',
        error: {
          title: '错误',
        },
        actions: {
          collapse: '收起',
          expand: '展开',
        },
        labels: {
          type: '类型',
          protocol: '协议',
          description: '描述',
          relatedLinks: '相关链接',
          dataIdList: '数据ID列表',
        },
        popover: {
          experiment: '实验',
          sample: '样本',
          run: '批次',
          analysis: '分析',
          data: '数据',
          fileName: '文件名',
          dataLinks: '数据链接',
        },
      },
      shareFromOther: {
        title: '来自他人的共享',
        filters: {
          sort: '排序',
          resourceType: '资源类型',
          year: '年份',
          select: '选择',
        },
        sort: {
          shareId: '共享ID',
          shareDate: '共享日期',
          status: '状态',
        },
        resourceTypes: {
          project: '项目',
          experiment: '实验',
          sample: '样本',
          analysis: '分析',
        },
        status: {
          share: '共享',
          sharing: '共享中',
        },
        actions: {
          exportDataLinks: '导出数据链接',
          dataIdList: '数据ID列表',
        },
        dates: {
          shareDate: '共享日期',
        },
        info: {
          shareEmail: '共享邮箱',
        },
      },
    },
  },
  browse: {
    sortButtons: {
      modifiedDate: '修改日期',
      name: '名称',
      submitter: '提交者',
      id: 'ID',
      type: '类型',
      description: '描述',
    },
    builderOptions: {
      general: {
        label: ' ',
        id: 'ID',
        name: '名称',
        description: '描述',
        usedIds: '已使用ID',
        modifiedDate: '修改日期',
      },
      experiment: {
        label: '实验',
        protocol: '协议',
        experimentType: '实验类型',
        libraryStrategy: '文库策略',
        libraryLayout: '文库布局',
        librarySelection: '文库选择',
        libraryName: '文库名称',
        platform: '平台',
      },
      sample: {
        label: '样本',
        sampleType: '样本类型',
        organism: '物种名称',
        tissue: '组织',
        subjectId: '受试者ID',
        biomaterialProvider: '生物材料提供者',
        disease: '疾病',
        disPhenotype: '疾病表型',
        mutationType: '突变类型',
        sampleLoc: '样本位置',
        gender: '性别',
        extractedMolType: '提取分子类型',
        devStage: '发育阶段',
        biome: '生物群落',
        envBiome: '环境生物群落',
        envMaterial: '环境材料',
        envFeature: '环境特征',
      },
      analysis: {
        label: '分析',
        analysisType: '分析类型',
        pipelineProgram: '流水线程序',
      },
      data: {
        label: '数据',
        fileType: '文件类型',
      },
      publish: {
        label: '文章',
        doi: 'DOI',
        pmid: 'PMID',
        articleName: '文章名称',
      },
      submitter: {
        label: '提交者',
        submitter: '提交者',
        organization: '组织机构',
        country: '国家',
      },
      fastQc: {
        label: '快速质控',
        numSeqs: '序列数量',
        bases: '碱基数',
        q20: 'Q20(%)',
        q30: 'Q30(%)',
      },
    },
    detail: {
      components: {
        association: {
          title: '关联信息',
          noData: '暂无数据',
          relatedExperiments: '相关实验',
        },
        attributes: {
          title: '属性',
          noData: '暂无数据',
        },
        authorInfo: {
          title: '作者信息',
          submission: '提交者',
          affiliatedUnit: '所属单位',
          createDate: '创建日期',
          lastModified: '最后修改',
        },
        expSapSingleTable: {
          searchPlaceholder: '搜索[ID]',
          search: '搜索',
          export: '导出',
          experiment: '实验',
          sample: '样本',
          experimentId: '实验ID',
          sampleId: '样本ID',
          experimentName: '实验名称',
          sampleName: '样本名称',
        },
        expSapTable: {
          experimentList: '实验列表',
          sampleList: '样本列表',
        },
        generalInfo: {
          title: '基本信息',
          tooltips: {
            exportDataLinks: '导出数据链接',
            gsaDataExport: 'GSA数据导出',
            sraDataExport: 'SRA数据导出',
            edit: '编辑',
            security: '安全设置',
            shareTo: '共享给',
            reviewTo: '审核给',
            requestRestrictedDatas: '申请受限数据',
          },
          labels: {
            projectId: '项目ID',
            relatedLinks: '相关链接',
            publications: '发表文献',
          },
          dialog: {
            title: '如何引用？',
            citationIntro:
              '成功将数据提交到NODE后，我们建议在您的手稿中使用以下措辞来描述数据存储：',
            citationText:
              '所有数据都可以在NODE（{originUrl}）中访问，登录号为{no}，或通过URL：{hrefUrl}',
            publicationPrompt: '请引用以下文章信息：',
            unaccessibleTip: '* 提示：此{type}不可访问',
            copy: '复制',
          },
          messages: {
            copySuccess: '复制成功',
          },
        },
        gsaExport: {
          title: '导出GSA数据',
          labels: {
            security: '安全性',
            experimentType: '实验类型',
            experimentId: '实验ID',
            subjectType: '样本类型',
            gsaTemplate: 'GSA模板',
          },
          searchPlaceholder: '搜索实验ID或名称',
          table: {
            experimentId: '实验ID',
            experimentName: '实验名称',
          },
          messages: {
            noSequencingDataSupport: 'GSA目前不支持非测序数据提交。',
            selectExperimentType: '请至少选择一种实验类型。',
            noExportableSubjectType: '当前项目下没有可导出的样本类型。',
          },
          buttons: {
            export: '导出',
            cancel: '取消',
          },
          validation: {
            selectSecurity: '请选择安全性',
            selectExperimentType: '请选择实验类型',
            selectExperiment: '请选择实验',
            selectSubjectType: '请选择样本类型',
          },
        },
        relatedAnalysisList: {
          title: '相关分析信息',
          table: {
            analysisId: '分析ID',
            analysisName: '分析名称',
            analysisType: '分析类型',
            data: '数据',
            submissionDate: '提交日期',
          },
          tooltip: {
            clickDisplayDataInfo: '点击显示数据信息',
          },
          dialog: {
            title: '数据信息',
            table: {
              dataId: '数据ID',
              dataName: '数据名称',
              dataSecurity: '数据安全性',
              dataSize: '数据大小',
              dataUploadTime: '数据上传时间',
            },
          },
          messages: {
            noDataFound: '未找到数据！',
          },
        },
        relatedDataList: {
          title: '数据列表',
          export: '导出',
          operate: '操作',
          publicDate: '公开日期：',
          tooltips: {
            htmlDownload: 'html下载',
            sftpDownload: 'sftp下载',
          },
          messages: {
            restrictedDataMessage:
              '此数据受限，需要事先批准才能下载。请登录以检查您是否具有必要的权限。',
            tip: '提示',
            login: '登录',
            cancel: '取消',
          },
        },
        relatedDataQcInfoList: {
          title: '数据质控信息',
          table: {
            dataId: '数据ID',
            dataName: '数据名称',
            dataSecurity: '数据安全性',
            format: '格式',
            type: '类型',
            numSeqs: '序列数',
            bases: '碱基数',
            minLen: '最小长度',
            avgLen: '平均长度',
            maxLen: '最大长度',
            q20: 'Q20(%)',
            q30: 'Q30(%)',
            avgQual: '平均质量',
            gc: 'GC(%)',
            operate: '操作',
          },
          tooltips: {
            viewFastqcReport: '查看FastQC报告',
            downloadFastqcReport: '下载FastQC报告',
            requestAccessFirst: '要查看质控信息，请先申请数据访问权限。',
          },
        },
        sraExport: {
          title: '导出SRA数据',
          labels: {
            security: '安全性',
            experimentType: '实验类型',
            experimentId: '实验ID',
            subjectType: '样本类型',
            sraTemplate: 'SRA模板',
          },
          searchPlaceholder: '搜索实验ID或名称',
          table: {
            experimentId: '实验ID',
            experimentName: '实验名称',
          },
          messages: {
            noSequencingDataSupport: 'SRA目前不支持非测序数据提交。',
            selectExperimentType: '请至少选择一种实验类型。',
            noExportableSubjectType: '当前项目下没有可导出的样本类型。',
          },
          buttons: {
            export: '导出',
            cancel: '取消',
          },
          validation: {
            selectSecurity: '请选择安全性',
            selectExperimentType: '请选择实验类型',
            selectExperiment: '请选择实验',
            selectSubjectType: '请选择样本类型',
          },
        },
        statDetail: {
          title: '统计详情',
          labels: {
            counts: '计数',
            sample: '样本',
            run: '批次',
            files: '文件',
            public: '公开',
            restricted: '受限',
            private: '私有',
          },
        },
        total: {
          title: '总计',
          tip: '（可访问 / 总计）',
        },
      },

      project: {
        breadcrumb: {
          my: '我的',
          title: '项目详情',
        },
        error: {
          title: '错误',
        },
        generalInfo: {
          projectId: '项目ID',
          projectName: '项目名称',
          description: '描述',
          usedIds: '历史ID',
        },
        statistics: {
          volume: '数据量',
          run: '批次',
          files: '文件',
          sample: '样本',
        },
        statDetail: {
          experimentalType: '按实验类型统计的案例和文件数量',
          organism: '按物种名称统计的案例和文件数量',
          fileTypes: '文件类型和访问限制',
        },
        columns: {
          experimentId: '实验ID',
          experimentName: '实验名称',
          experimentType: '实验类型',
          experimentDescription: '实验描述',
          sampleId: '样本ID',
          sampleName: '样本名称',
          sampleType: '样本类型',
          sampleDescription: '样本描述',
          runId: '批次ID',
          runName: '批次名称',
          dataId: '数据ID',
          dataName: '数据名称',
          dataSecurity: '数据安全性',
          dataSize: '数据大小',
          dataUploadTime: '数据上传时间',
        },
      },
      experiment: {
        breadcrumb: {
          my: '我的',
          title: '实验详情',
        },
        error: {
          pageTitle: '实验详情',
          title: '错误',
        },
        generalInfo: {
          experimentId: '实验ID',
          experimentType: '实验类型',
          experimentName: '实验名称',
          projectNo: '项目编号',
          description: '描述',
          usedIds: '历史ID',
          protocol: '协议',
        },
        statDetail: {
          organism: '按物种名称统计的案例和文件数量',
          fileTypes: '文件类型和访问限制',
        },
        columns: {
          projectId: '项目ID',
          projectName: '项目名称',
          projectDescription: '项目描述',
          sampleId: '样本ID',
          sampleName: '样本名称',
          sampleType: '样本类型',
          sampleDescription: '样本描述',
          runId: '批次ID',
          runName: '批次名称',
          dataId: '数据ID',
          dataName: '数据名称',
          dataSecurity: '数据安全性',
          dataSize: '数据大小',
          dataUploadTime: '数据上传时间',
        },
      },
      run: {
        breadcrumb: {
          my: '我的',
          title: '批次详情',
        },
        content: {
          accessPrompt: '您想要访问与此批次相关的实验或样本吗？',
        },
        error: {
          title: '错误',
        },
      },
      sample: {
        breadcrumb: {
          my: '我的',
          title: '样本详情',
        },
        association: {
          title: '关联信息',
          noData: '暂无数据',
          relatedProject: '相关项目',
        },
        error: {
          title: '错误',
        },
        generalInfo: {
          sampleId: '样本ID',
          sampleType: '样本类型',
          sampleName: '样本名称',
          organism: '物种名称',
          tissue: '组织',
          subjectId: '受试者ID',
          description: '描述',
          usedIds: '历史ID',
          protocol: '协议',
        },
        statDetail: {
          experimentalType: '按实验类型统计的案例和文件数量',
          fileTypes: '文件类型和访问限制',
        },
        columns: {
          projectId: '项目ID',
          projectName: '项目名称',
          projectDescription: '项目描述',
          experimentId: '实验ID',
          experimentName: '实验名称',
          experimentType: '实验类型',
          experimentDescription: '实验描述',
          runId: '批次ID',
          runName: '批次名称',
          dataId: '数据ID',
          dataName: '数据名称',
          dataSecurity: '数据安全性',
          dataSize: '数据大小',
          dataUploadTime: '数据上传时间',
        },
      },
      analysis: {
        breadcrumb: {
          my: '我的',
          title: '分析详情',
        },
        error: {
          title: '错误',
        },
        generalInfo: {
          analysisId: '分析ID',
          analysisName: '分析名称',
          analysisType: '分析类型',
          description: '描述',
          usedIds: '历史ID',
        },
        statistics: {
          volume: '数据量',
          files: '文件',
        },
        pipeline: {
          title: '流程',
          label: '流程',
          columns: {
            program: '程序',
            version: '版本',
            notes: '备注',
            link: '链接',
          },
          output: {
            title: '输出',
            columns: {
              dataId: '数据ID',
              dataName: '数据名称',
              dataType: '数据类型',
              uploadTime: '上传时间',
              security: '安全性',
              md5: 'MD5',
              operate: '操作',
            },
            tooltips: {
              htmlDownload: 'html下载',
              sftpDownload: 'sftp下载',
            },
          },
        },
        target: {
          title: '目标',
          label: '目标',
          otherTarget: '其他目标',
        },
        columns: {
          dataId: '数据ID',
          dataName: '数据名称',
          dataType: '数据类型',
          dataSecurity: '数据安全性',
          dataSize: '数据大小',
          dataUploadTime: '数据上传时间',
        },
      },
    },
    index: {
      breadcrumb: {
        browse: '浏览',
        advancedSearch: '高级搜索',
      },
      search: {
        placeholder: '搜索',
        advancedSearch: '高级搜索',
        example: '例如',
      },
      advancedSearch: {
        title: 'NODE 高级搜索构建器',
        builderPlaceholder: '使用下面的构建器创建您的搜索',
        cancel: '取消',
        edit: '编辑',
        clear: '清除',
        builder: '构建器',
        pleaseSelect: '请选择',
        datePicker: {
          to: '至',
          startDate: '开始日期',
          endDate: '结束日期',
        },
        boolOptions: {
          yes: '是',
          no: '否',
        },
        search: '搜索',
        close: '关闭',
      },
      filters: {
        access: '访问权限',
        more: '更多>',
        less: '收起',
        showAdditionalFilters: '显示附加筛选器',
        additionalFilters: '附加筛选器',
        confirm: '确认',
      },
      tabs: {
        verboseMode: '详细模式',
        conciseList: '简洁列表',
      },
      content: {
        usedId: '历史ID：',
        experiments: '实验',
        samples: '样本',
        files: '文件',
        experimentType: '实验类型',
        sampleType: '样本类型',
        analysisType: '分析类型',
        organism: '物种名称',
        tissue: '组织',
        description: '描述',
        noDataMessage: '数据不存在、不公开或已撤回。',
      },
      table: {
        columns: {
          id: 'ID',
          type: '类型',
          name: '名称',
          description: '描述',
          submitter: '提交者',
          modifiedDate: '修改日期',
        },
      },
      actions: {
        exportDataLinks: '导出数据链接',
      },
    },
  },
  featureData: {
    index: {
      breadcrumb: '特色数据',
      navigation: {
        humanResource: '人类资源',
        microbeResource: '微生物资源',
        omicsResource: '组学资源',
        hydrosphere: '水圈',
      },
    },
    hmdsNsfcDataSet: {
      title: '水圈',
      units: {
        tbase: 'Tbase',
        samples: '样本',
      },
      table: {
        title: '可访问样本列表',
        columns: {
          sampleId: '样本ID',
          sampleName: '样本名称',
          description: '描述',
          sampleType: '样本类型',
          organism: '物种名称',
          curatedBiome: '策划生物群落',
        },
        notProvided: '未提供',
      },
    },
    humanRes: {
      titles: {
        humanResource: '人类资源',
        microbeResource: '微生物资源',
      },
      statistics: {
        project: '项目',
        samples: '样本',
        sampleTypes: '样本类型',
        experiments: '实验',
        experimentTypes: '实验类型',
      },
      tables: {
        projectList: '项目列表',
        sampleList: '样本列表',
        columns: {
          projectId: '项目ID',
          projectName: '项目名称',
          description: '描述',
          experimentType: '实验类型',
          sampleId: '样本ID',
          sampleName: '样本名称',
          sampleType: '样本类型',
          organism: '物种名称',
          tissue: '组织',
        },
      },
    },
    omics: {
      index: {
        tabs: {
          multipleOmicsResource: '多组学资源',
          multipleSampleResource: '多样本资源',
          singleSampleMultiOmics: '单样本多组学',
        },
      },
      omicsRes: {
        searchPlaceholder: '搜索ID',
        searchButton: '搜索',
        columns: {
          projectId: '项目ID',
          sampleId: '样本ID',
        },
        filterTypes: {
          sampleType: '样本类型',
          experimentType: '实验类型',
        },
      },
      sampleOmics: {
        searchPlaceholder: '搜索ID',
        searchButton: '搜索',
        filterType: '实验类型',
        columns: {
          sampleId: '样本ID',
          sampleType: '样本类型',
          genomic: '基因组学',
          transcriptomic: '转录组学',
          transcriptomicSingleCell: '单细胞转录组学',
          microarray: '基因芯片',
          proteomic: '蛋白质组学',
          metagenomic: '宏基因组学',
          metatranscriptomic: '宏转录组学',
          metabolomic: '代谢组学',
          other: '其他',
        },
      },
      sampleRes: {
        searchPlaceholder: '搜索ID',
        searchButton: '搜索',
        filterType: '样本类型',
        columns: {
          projectId: '项目ID',
          human: '人类',
          animalia: '动物界',
          plantae: '植物界',
          pathogenAffectingPublicHealth: '影响公共健康的病原体',
          cellLine: '细胞系',
          environmentHost: '环境宿主',
          environmentNonHost: '环境非宿主',
          microbe: '微生物',
        },
      },
    },
  },
  requestTo: {
    dialog: {
      title: '数据申请',
    },
    alert: {
      restrictedDataInfo: '1. 此申请列表仅列出需要授权后才能访问的受限数据。',
      directSendInfo: '2. 此申请将直接发送给对象的作者。',
      tooManyObjectsWarning: '3. 如果您选择太多对象，作者可能不会接受申请。',
    },
    form: {
      experimentId: '实验ID',
      sampleId: '样本ID',
      runId: '批次ID',
      dataId: '数据ID',
      placeholder: '请输入关键词',
      requestText: '申请理由',
    },
    buttons: {
      search: '搜索',
      reset: '重置',
      confirm: '确认',
      cancel: '取消',
    },
    table: {
      dataId: '数据ID',
      dataName: '数据名称',
      experiment: '实验',
      sample: '样本',
      run: '批次',
      analysis: '分析',
    },
    messages: {
      applicationSubmitted: '您已提交申请。请耐心等待数据所有者授权。',
      pleaseSelectData: '请选择数据',
      requesting: '申请中',
      applySuccess: '申请成功',
    },
  },
  reviewTo: {
    dialog: {
      title: '审阅至',
    },
    alert: {
      urlInvalidInfo: '1. 审阅URL将在有效期后失效。',
      securitySuggestion: '2. 我们建议在有效期后将安全性设置为公开。',
      periodSuggestion: '3. 我们建议将有效期设置为3个月以上。',
    },
    form: {
      name: '姓名',
      email: '邮箱',
      validPeriod: '有效期',
      pickDate: '选择日期',
    },
    dataTypes: {
      project: '项目',
      analysis: '分析',
      experiment: '实验',
      sample: '样本',
    },
    buttons: {
      confirm: '确认',
      cancel: '取消',
    },
    validation: {
      emailFormat: '请输入正确的邮箱地址',
      dateRequired: '请选择过期日期',
    },
    messages: {
      selectAtLeastOne: '请至少选择一个数据',
      shortPeriodWarning:
        '有效期较短！我们建议将有效期设置为3个月以上。您确定要继续吗？',
      sending: '发送中',
      reviewSuccess: '审阅成功',
      linkIs: '链接是：',
      saveCarefully: '请仔细保存链接！任何人都可以通过此链接访问数据。',
    },
  },
  security: {
    dialog: {
      title: '安全状态变更确认',
    },
    filter: {
      instruction: '1.筛选您要修改安全状态的数据：',
    },
    form: {
      security: '安全性',
      experimentId: '实验ID',
      sample: '样本',
      placeholder: '请输入关键词',
      select: '选择',
    },
    securityLevels: {
      private: '私有',
      restricted: '受限',
      public: '公开',
    },
    sampleOptions: {
      sampleId: '样本ID',
      sampleName: '样本名称',
      sampleType: '样本类型',
      sampleOrganism: '样本物种名称',
    },
    buttons: {
      filter: '筛选',
      confirm: '确认',
      cancel: '取消',
    },
    tabs: {
      data: '数据',
      analysisData: '分析数据',
      relatedAnalysisData: '相关分析数据',
    },
    table: {
      dataId: '数据ID',
      dataName: '数据名称',
      experiment: '实验',
      sample: '样本',
      run: '批次',
      analysis: '分析',
      dataType: '数据类型',
      uploadTime: '上传时间',
      nameSecurity: '名称/安全性',
      analysisId: '分析ID',
      analysisName: '分析名称',
    },
    change: {
      instruction: '2.将上述查询的数据和分析数据从',
      to: '更改为：',
      qualityControlInfo: '数据的质量控制信息也将同步公开。',
    },
    restrictedOptions: {
      until: '直到',
      requireRequest: '需要申请访问',
    },
    humanGenetic: {
      regulationText:
        '根据《中华人民共和国人类遗传资源管理条例》和《人类遗传资源管理条例实施细则》，人类遗传资源数据的公开需要通过人类遗传资源审阅进行审阅和备案。请如实填写相应的备案号。如有疑问，请联系',
      phoneNumber: '或 010-88225151',
      biologicalSamples:
        '可能含有极少量脱落、残留或游离细胞或基因的尿液、粪便、血清、血浆等生物样本',
      otherSamples: '其他样本',
      nonHuman: '非人类',
      human: '人类',
      humanCommercialCellLines: '人类商业细胞系',
      humanNonCommercialCellLines: '人类非商业细胞系',
      filingNumberPlaceholder: '请输入备案号',
    },
    messages: {
      loading: '加载中...',
      securityModificationError: '现在无法执行安全修改，因为：<br>{reasons}',
      auditStatusWarning:
        '<p>您的账户状态不是"审核通过"，无法更新数据安全性！</p><span class="text-danger">*</span>如果您最近收到了"审核通过"邮件，但用户中心的账户状态不是"审核通过"，请退出网站并重新登录！',
      selectSecurityLevel: '请选择要修改的安全级别！',
      selectDataToUpdate: '请选择要更新安全级别的数据',
      multipleSampleTypes:
        '样本类型中有 {types}。请逐一选择样本类型来更改数据的安全性。',
      submitting: '提交中',
      modifiedSuccessfully: '修改成功',
      modifiedSuccessfullyWait:
        '修改成功！如果您发现数据的安全性没有改变，请耐心等待几分钟并再次刷新浏览器',
      tip: '提示',
      ok: '确定',
      fillFilingNumber: '请填写备案号',
      invalidFormat: '格式无效，仅支持数字、字母、-',
      dataLimitExceeded: '选择的数据数量超过1000的限制',
      publicSecurityConfirm:
        '数据安全级别更改为"公开"后，"数据质控信息"也将可见。',
    },
  },
  shareTo: {
    dialog: {
      title: '共享至',
    },
    form: {
      shareToEmail: '共享至邮箱 ({count}):',
      emailPlaceholder: '邮箱',
    },
    dataTypes: {
      project: '项目',
      experiment: '实验',
      sample: '样本',
      analysis: '分析',
    },
    empty: {
      noData: '无数据',
    },
    buttons: {
      confirm: '确认',
      cancel: '取消',
    },
    messages: {
      emailFormatError: '邮箱格式错误：[{emails}]',
      pleaseSelectEmail: '请选择邮箱',
      selectAtLeastOne: '请至少选择一项',
      sharing: '共享中',
      shareSuccess: '共享成功',
    },
  },
  submissionDetail: {
    status: {
      dataProcessing: '数据处理中',
      waitingReview: '等待审核',
      rejected: '已拒绝',
    },
    info: {
      lastModify: '最后修改',
      auditor: '审核员',
    },
    rejection: {
      title: '拒绝原因',
    },
    sections: {
      project: '项目',
      experiment: '实验',
      sample: '样本',
      archiving: '归档',
      analysis: '分析',
      publications: '文章信息',
      submitter: '提交者',
    },
    project: {
      projectId: '项目ID',
      projectName: '项目名称',
      projectDescription: '项目描述',
      relatedLinks: '相关链接{index}',
      autoAllocated: '检查后将自动分配。',
    },
    publications: {
      relatedId: '相关ID',
      relatedName: '相关名称',
      publishTitle: '发表标题',
      creator: '创建者',
      publishCreateDate: '发表创建日期',
    },
    submitter: {
      firstName: '名',
      middleName: '中间名',
      lastName: '姓',
      organization: '组织机构',
      department: '部门',
      piName: 'PI姓名',
      email: '邮箱',
      phone: '电话',
      fax: '传真',
      street: '街道',
      city: '城市',
      stateProvince: '州/省',
      postalCode: '邮政编码',
      countryRegion: '国家/地区',
    },
    buttons: {
      submit: '提交',
      edit: '编辑',
      delete: '删除',
      back: '返回',
    },
    messages: {
      confirmDelete: '确认删除提交：{subNo}？',
      deleteWarning: '删除后数据无法恢复，请谨慎操作',
      deleting: '删除中，请稍候',
      deleteSuccess: '删除成功',
      submissionNoEmpty: '提交编号不能为空',
      confirmSubmit: '确认提交数据：{subNo}？',
      submitting: '提交中，请稍候',
      error: '错误',
    },
  },
  hotTable: {
    labels: {
      type: '类型',
    },
    buttons: {
      columnVisibility: '列显示',
      selectAll: '全选',
      confirm: '确认',
      default: '默认',
      export: '导出',
    },
    placeholders: {
      searchColumn: '搜索列',
    },
  },
  expSapAttrDialog: {
    title: '{baseType}属性 ({currType})',
    labels: {
      attributeName: '属性名称',
    },
    table: {
      attributeName: '属性名称',
      description: '描述',
      valueFormat: '值格式',
    },
    placeholders: {
      searchTaxonomy: '请输入搜索分类',
      search: '请输入搜索',
      searchDisease: '请输入搜索疾病',
      select: '选择',
    },
  },
  pipeline: {
    step: '步骤-{step}',
    form: {
      program: '程序',
      link: '链接',
      version: '版本',
      output: '输出',
      notes: '备注',
    },
    placeholders: {
      selectOutput: '请选择输出',
    },
    options: {
      noData: '无数据',
    },
  },
  target: {
    title: '目标-{index}',
    types: {
      projectId: '项目ID',
      experimentId: '实验ID',
      sampleId: '样本ID',
      runId: '批次ID',
      dataId: '数据ID',
      analysisId: '分析ID',
    },
    form: {
      id: 'ID',
    },
    placeholders: {
      selectId: '请选择{type}',
      textareaExample:
        '在表单字段中输入标识符，用空格或换行分隔，例如：\n{example}',
    },
    options: {
      noData: '无数据',
    },
  },
  otherTarget: {
    title: '其他目标源',
    labels: {
      name: '名称',
      link: '链接',
    },
  },
};
